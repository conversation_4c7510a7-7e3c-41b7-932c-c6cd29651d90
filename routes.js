const express = require("express");
const path = require("path");
const fs = require("fs");
const router = express.Router();
const {sockserver} = require("./index");
const {rosterReadFile} = require("./js/compDataInput.js");
const {FILE_STARTLIST, FILE_RESULTS, SYSTEM_E4S, TYPE_PF_IMAGE, TYPE_PF_RESULTS} = require("./js/serverConstants");
const {getEvents} = require("./controllers/event");
const {
    competition,
    updateInfo,
    getInfo,
} = require("./controllers/competition");
const touchController = require("./controllers/touch");
const {writeLog} = require("./controllers/log");
const Constants = require("./js/serverConstants");
const comp = require("./controllers/competition");
const {updateConfig} = require("./controllers/config");

const sentImages = new Set(); // Track which images have been sent

function getImageName(eventDetails) {
    let fileName = "";
    // does the eventCode ( discipline ) show images
    // if (pfImageArr.includes(eventCode)) {
    let heatNumber = eventDetails.HeatNumber;
    if (heatNumber < 10) {
        heatNumber = "0" + heatNumber;
    }
    let photoFolder = global.compObj?.photoFolder;
    if (!photoFolder || photoFolder === "") {
        photoFolder = "P:/";
    }
    fileName = eventDetails.EventNumber + "-" + eventDetails.RoundNumber + "-" + heatNumber + ".jpg";
    let filePath = photoFolder + fileName;
    try {
        fs.accessSync(filePath, fs.constants.F_OK);
        router.use(express.static(photoFolder));
        fileName = "/" + fileName;
    } catch (err) {
        fileName = "";
    }
    // }

    return fileName;
}

router.post("/lynx", (req, res) => {
    const urlParams = new URLSearchParams(req.url.split("?")[1]);
    const boardId = urlParams.get("boardid");
    if (boardId) {
        req.body.boardId = boardId;
    }
    const system = urlParams.get("system");
    if (system) {
        req.body.systemId = system;
    }
    const body = req.body;
    if ( body.ContentType === Constants.TYPE_PF_START) {
        // body.serverStartTime = new Date().toLocaleTimeString("en-GB", { hour12: false }) + "." + String(new Date().getMilliseconds()).padStart(2, "0");
    }
    const bodyString = JSON.stringify(body);
    // Check if ContentType is Race Results and handle image
    if (body.ContentType === TYPE_PF_RESULTS) {
        // Check if there's an image to send
        const eventDetails = body.EventDetails;
        
        // only send image if it hasn't been sent before
        if (eventDetails) {
            // Create a unique key for this event
            const imageKey = `${eventDetails.EventNumber}-${eventDetails.RoundNumber}-${eventDetails.HeatNumber}`;
            
            // Path to check for event image
            const imagePath = getImageName(eventDetails);

            // Check if image exists and hasn't been sent before
            if (imagePath !== "" && !sentImages.has(imageKey)) {
                // Create image message
                const imageMessage = {
                    ContentType: TYPE_PF_IMAGE,
                    EventDetails: eventDetails,
                    imagePath: imagePath
                };

                // Send image message to clients
                sockserver.clients.forEach((client) => {
                    client.send(JSON.stringify(imageMessage));
                });
                
                // Mark this image as sent
                sentImages.add(imageKey);
            }
        }
    }

    console.log(bodyString);
    sockserver.clients.forEach((client) => {
        client.send(bodyString);
    });
    writeLog(global.compObj.compId, bodyString);
    res.send("success");
});

router.use(express.static(path.join(__dirname, "/node_modules")));
router.use(express.static(path.join(__dirname, "/")));
router.use(express.static(path.join(__dirname, "/style")));
router.use(express.static(path.join(__dirname, "/js")));
router.use("/controller", (req, res) =>
    res.sendFile("/controller.html", {root: __dirname}),
);
router.use("/controllerBody", (req, res) =>
    res.sendFile("/controllerBody.html", {root: __dirname}),
);
router.use("/board", (req, res) =>
    res.sendFile("/board.html", {root: __dirname}),
);
router.use("/boardBody", (req, res) =>
    res.sendFile("/boardBody.html", {root: __dirname}),
);
router.use("/reader", (req, res) =>
    res.sendFile("/reader/reader.html", {root: __dirname}),
);
// create a route setCompId which will receive compId
router.get("/setCompId", (req, res) => {
    const compId = req.query.compId;
    if (compId) {
        global.compObj.compId = parseInt(compId);
        global.compObj.type = SYSTEM_E4S;
        // call and end point on entry4sports.co.uk, passing the compid to retrieve competition info
        fetch("https://entry4sports.co.uk/wp-json/e4s/v5/competition/schedule/" + compId)
            .then((response) => response.json())
            .then((data) => {
                if (data.errNo === 0) {
                    console.log("Received E4S Competition Info...");
                    const comp = require("./controllers/competition.js");
                    comp.e4sUpdateInfo(data.data);
                    // now process the events
                    const {writeE4SEvent} = require("./controllers/event");
                    data.data.schedule.forEach(event => writeE4SEvent(event));
                }
            });
        res.status(200).send("Competition ID set to: " + compId);
    } else {
        res.status(400).send("Competition ID is required.");
    }
});
// Add new route to set competition type
router.get("/setCompType", (req, res) => {
    const type = req.query.type;
    // console.log("Received message to Set competition type to: " + type);
    if (type) {
        // Import the setCompType function from compDataInput.js
        const {setCompType} = require("./js/compDataInput.js");
        // Call the function with the provided type
        setCompType(type);
        res.status(200).send("Competition type set to: " + type);
    } else {
        res.status(400).send("Competition type is required.");
    }
});
router.get("/readSLFile", (req, res) => {
    const filePath = req.query.file;
    const reset = req.query.reset === "true";
    if (filePath) {
        const readRetVal = rosterReadFile(filePath, FILE_STARTLIST, reset);
        if (readRetVal !== false) {
            res.status(200).send("Read file : " + readRetVal);
        } else {
            res.status(400).send("Read file : " + readRetVal);
        }
    } else {
        res.status(400).send("File path is required");
    }
});
router.get("/getId", (req, res) => {
    res.status(200).send(global.compObj.compId.toString());
});
router.get("/getInfo", (req, res) => {
    let compId = req.query.compid;
    if (compId && parseInt(compId) !== 0) {
        global.compObj.compId = parseInt(compId);
    }
    if (global.compObj.compId !== 0) {
        getInfo(global.compObj.compId).then(function (info) {
            res.status(200).send(info);
        });
    } else {
        res.status(400).send("Competition ID is required");
    }
    return;
});
router.get("/readRFile", (req, res) => {
    const filePath = req.query.file;
    const reset = req.query.reset === "true";
    if (filePath) {
        const readRetVal = rosterReadFile(filePath, FILE_RESULTS, reset);
        if (readRetVal !== false) {
            res.status(200).send("File read : " + readRetVal);
        } else {
            res.status(400).send("File read : " + readRetVal);
        }
    } else {
        res.status(400).send("File path is required");
    }
    return;
});
router.get("/getevents", (req, res) => {
    // set compId variable to the url parameter compid
    let compId = req.query.compid;
    if (compId === undefined || compId === "0") {
        compId = global.compObj.compId;
    } else {
        compId = parseInt(compId);
        global.compObj.compId = compId;
    }
    let compObj = getInfo(compId);
    if (compId === 0 || compId === undefined) {
        res
            .status(200)
            .send("compCompetition ID is required. Run the reader process");
        return;
    }
    let eventDate = req.query.date;

    const {getEvents} = require("./controllers/event");
    getEvents(compId, eventDate).then(function (events) {
        res.status(200).send(events);
    });
    return;
});
router.post("/log", (req, res) => {
    const {writeLog} = require("./controllers/log");

    let logText = req.body.log;

    if (typeof logText === "object") {
        logText = JSON.stringify(logText);
    }

    console.log("Writing log logText:", logText);

    let compId = req.body.compId;
    if (compId === undefined) {
        compId = global.compObj.compId;
    }
    writeLog(compId, logText);
    res.status(200).send({
        message: "Log written",
    });
});
router.post("/updatecomp", (req, res) => {
    let compInfo = req.body;
    const {updateInfo} = require("./controllers/competition");
    console.log("Updating Competition Info", compInfo);
    
    // Store the imagePath in the global object but don't send to database
    if (compInfo.imagePath) {
        global.compObj.imagePath = compInfo.imagePath;
    }
    
    if (updateInfo(compInfo)) {
        res.status(200).send({
            message: "Competition updated",
        });
    } else {
        res.status(200).send({
            message: "Competition NOT updated. Check the logs",
        });
    }
});
router.get("/getConfig", (req, res) => {
    res.status(200).send(JSON.stringify(global.compObj));
});
router.post("/updateconfig", (req, res) => {
    let compInfo = req.body;
    const { updateConfig } = require("./controllers/config");

    global.compObj = compInfo;
    if (updateConfig(compInfo)) {
        res.status(200).send({
            message: "Configuration updated",
        });
        sockserver.clients.forEach((client) => {
            if (client.readyState === client.OPEN) {
                client.send(
                    JSON.stringify({
                        ContentType: "compInfo",
                        config: global.compObj
                    }),
                );
            }
        });
    } else {
        res.status(200).send({
            message: "Configuration NOT updated. Check the logs",
        });
    }
});
router.post("/updatecallroomconfig", (req, res) => {
    let compInfo = req.body;
    const {updateInfo} = require("./controllers/competition");
    console.log("Updating Callroom Config", compInfo);
    if (updateInfo(compInfo)) {
        res.status(200).send({
            message: "Competition updated",
        });
        sockserver.clients.forEach((client) => {
            if (client.readyState === client.OPEN) {
                client.send(
                    JSON.stringify({
                        ContentType: "updateCallroomConfig",
                    }),
                );
            }
        });
    } else {
        res.status(200).send({
            message: "Competition NOT updated. Check the logs",
        });
    }
});
router.post("/updateevents", (req, res) => {
    // get events from payload
    let events = req.body;
    const {updateEvents} = require("./controllers/event");
    const {sockserver} = require("./index");
    updateEvents(events).then(function (newEvents) {
        let payload = {
            ContentType: "updateCallRoom",
            events: newEvents,
        };
        sockserver.clients.forEach((client) => {
            if (client.readyState === client.OPEN) {
                client.send(JSON.stringify(payload));
            }
        });
        res.status(200).send(newEvents);
    });
});
router.get("/touchdata", (req, res) => {
    let compId = req.query.compid;
    if (compId === undefined) {
        compId = global.compObj.compId;
    } else {
        compId = parseInt(compId);
        global.compObj.compId = compId;
    }
    if (global.compObj.compId === 0) {
        res
            .status(400)
            .send(
                '{"errNo": 1, "error": "Competition ID is required. Run the reader process"}',
            );
        return;
    }
    if (global.touchObj === null) {
        const touchController = require("./controllers/touch");
        (async () => {
            let touchObj = await touchController.init();
            global.touchObj = touchObj;
            res.status(200).send(global.touchObj);
        })();
    } else {
        res.status(200).send(global.touchObj);
    }
    return;
});
router.use("/callroom", (req, res) =>
    res.sendFile("/src/views/callroom.html", {root: __dirname}),
);
// create a POST route to generate a BTShow
router.post("/generateBTShow", (req, res) => {
   // get post payload
    const payload = req.body;
    const {processBTRequest} = require("./controllers/buffTitler");
    processBTRequest(payload);
    res.status(200).send("BTShow generated");
});
// Add route to get images from a folder
router.get("/getFileTypes", (req, res) => {
    const folder = req.query.folder;
    let btContent = false;
    let btVideos = false;
    let videos = false;
    let images = false;
    if (!folder) {
        return res.status(400).json({ error: "No folder specified" });
    }
    let type = req.query.type;
    if (type === "v") {
        videos = true;
    } else if (type === "b") {
        btContent = true;
    }else if (type === "bt") {
        btVideos = true;
    } else {
        images = true;
    }
    try {
        // Resolve the folder path relative to the server root
        const folderPath = path.resolve(__dirname, folder);
        
        // Read the directory
        fs.readdir(folderPath, (err, files) => {
            if (err) {
                console.error("Error reading directory:", err);
                return res.status(500).json({ error: "Error reading directory" });
            }
            
            // Filter for files based on type
            const useExtensions = videos || btVideos ? ['.mp4', '.avi', '.mov', '.wmv'] : images ? ['.jpg', '.jpeg', '.png', '.gif', '.bmp'] : ['.bt'];
            const filesOfType = files.filter(file => {
                const ext = path.extname(file).toLowerCase();
                return useExtensions.includes(ext);
            });
            
            // If btContent, read each .bt file and extract XML properties
            if (btContent) {
                const btFilesWithProperties = filesOfType.map(file => {
                    const filePath = path.join(folderPath, file);
                    const messageDescriptions = [];
                    
                    try {
                        const content = fs.readFileSync(filePath, 'utf8');
                        const lines = content.split('\n');
                        
                        let currentName = null;
                        let lineCount = 0;
                        for (const line of lines) {
                            lineCount++;
                            const trimmedLine = line.trim();
                            
                            // Look for NAM lines
                            if (trimmedLine.startsWith('\0N\0A\0M')) {
                                const nameParts = trimmedLine.replaceAll("\0","").split('\t');
                                if (nameParts.length > 1) {
                                    currentName = nameParts[1].replace(/"/g, '').trim(); // Remove quotes
                                }
                            }
                            
                            // Look for TXT lines with xml: pattern
                            if (trimmedLine.startsWith('\0T\0X\0T') && trimmedLine.includes('\0x\0m\0l')) {
                                const txtParts = trimmedLine.replaceAll("\0","").split('\t');
                                if (txtParts.length > 1) {
                                    const txtValue = txtParts[1].replace(/"/g, ''); // Remove quotes
                                    if (txtValue.includes('xml:') && txtValue.includes('T4S/')) {
                                        const xmlMatch = txtValue.match(/T4S\/(\w+)/);
                                        if (xmlMatch && currentName) {
                                            messageDescriptions.push({
                                                name: currentName,
                                                description: xmlMatch[1].replace("line","message")
                                            });
                                        }
                                    }
                                }
                                currentName = null; // Reset after processing TXT
                            }
                        }
                    } catch (readError) {
                        console.error(`Error reading .bt file ${file}:`, readError);
                    }
                    
                    return {
                        filename: file,
                        descriptions: messageDescriptions
                    };
                });
                
                res.json({ files: btFilesWithProperties });
            } else {
                res.json({ files: filesOfType });
            }
        });
    } catch (error) {
        console.error("Error:", error);
        res.status(500).json({ error: "Server error" });
    }
});module.exports = router;
