<div id="controller-tabs">
    <ul>
        <li><a href="#tab-competition">Competition Information</a></li>
        <li><a href="#tab-transitions">Transitions</a></li>
        <li><a href="#tab-board">Board Settings</a></li>
        <li><a href="#tab-message-styles">Message Styles</a></li>
        <li><a href="#tab-actions">Actions</a></li>
        <li><a href="#tab-messages">Message History</a></li>
    </ul>
    <div id="tab-competition">
        <fieldset>
            <legend>Competition Information</legend>
            <div>
                <div>Number: <span id="compId"></span></div>
                <div>Name: <input type="text" id="compName" class="compName" name="compName"></div>
            </div>
            <div>
                Event Name Format: <input id="eventNameFormat" class="eventNameFormat" name="eventNameFormat">
                <div class="eventNameHelper">Helpers : {{EVENTSTART}} {{EVENTNAME}} {{EVENTTITLE}} {{EVENTAGE}} {{EVENTGENDER}} {{EVENTSTAGE}}</div>
            </div>
            <div>
                PhotoFinish Image Folder: <input id="photoFolder" class="photoFolder" name="photoFolder" placeholder="p:/">
            </div>
            <div>
                Image Folder: <input id="imagePath" class="imagePath" name="imagePath" placeholder="Relative path from server (e.g., 'images')">
            </div>
            <div>
                Video Folder: <input id="videoPath" class="videoPath" name="videoPath" placeholder="Relative path from server (e.g., 'videos')">
            </div>
            <div>
                BluffTitler Drive Letter: 
                <select id="bluffDrive" name="bluffDrive">
                    <option value="">Select Drive</option>
                    <option value="A">A:</option>
                    <option value="B">B:</option>
                    <option value="C">C:</option>
                    <option value="D">D:</option>
                    <option value="E">E:</option>
                    <option value="F">F:</option>
                    <option value="G">G:</option>
                    <option value="H">H:</option>
                    <option value="I">I:</option>
                    <option value="J">J:</option>
                    <option value="K">K:</option>
                    <option value="L">L:</option>
                    <option value="M">M:</option>
                    <option value="N">N:</option>
                    <option value="O">O:</option>
                    <option value="P">P:</option>
                    <option value="Q">Q:</option>
                    <option value="R">R:</option>
                    <option value="S">S:</option>
                    <option value="T">T:</option>
                    <option value="U">U:</option>
                    <option value="V">V:</option>
                    <option value="W">W:</option>
                    <option value="X">X:</option>
                    <option value="Y">Y:</option>
                    <option value="Z">Z:</option>
                </select>
            </div>
            <div>
                BluffTitler Folder: <input id="bluffPath" class="bluffPath" name="bluffPath" placeholder="Relative path from server (e.g., 'shows')">
            </div>
            <div>
                Branding :
                <select id="brandingStyle" name="brandingStyle">
                    <option value="BIG">BIG</option>
                    <option value="Birchfield">Birchfield</option>
                    <option value="EA">EA</option>
                    <option value="ESAA">ESAA</option>
                    <option value="Loughborough">Loughborough</option>
                    <option value="NPS">NPS</option>
                    <option value="T4S">T4S</option>
                </select>
            </div>
            <button id="saveCompBtn">Save Company Information</button>
        </fieldset>
    </div>
    <div id="tab-transitions">
        <fieldset>
            <legend>Transitions</legend>
            <div style="margin-bottom:10px;">
                <button id="saveTransitionsBtn">Save Transitions</button>
            </div>
            <div style="">
                <div>
                    <input type="checkbox" id="transitionClock" name="transitionOption" value="Clock" onchange="toggleTransitionFields(TYPE_LIVE_CLOCK)">
                    <label for="transitionClock">Clock</label>
                    <div id="transitionFields_Clock" class="transitionFields" style="display:none; margin-top: 5px;">
                        File path : <input id="transitionFile_Clock" name="transitionFile_Clock" style="width: 50%;">
                        Duration (secs) : <input type="number" id="transitionDuration_Clock" name="transitionDuration_Clock" min="1" value="10" placeholder="Duration (sec)" style="width: 10%;">
                    </div>
                </div>
                <div>
                    <input type="checkbox" id="transitionRaceReset" name="transitionOption" value="RaceReset" onchange="toggleTransitionFields(TYPE_PF_RESET)">
                    <label for="transitionRaceReset">Race Reset</label>
                    <div id="transitionFields_RaceReset" class="transitionFields" style="display:none; margin-top: 5px;">
                        File path : <input id="transitionFile_RaceReset" name="transitionFile_RaceReset" style="width: 50%;">
                        Duration (secs) : <input type="number" id="transitionDuration_RaceReset" name="transitionDuration_RaceReset" min="1" value="10" placeholder="Duration (sec)" style="width: 10%;">
                    </div>
                </div>
                <div>
                    <input type="checkbox" id="transitionRaceStartList" name="transitionOption" value="Race Start List" onchange="toggleTransitionFields(TYPE_PF_STARTLIST)">
                    <label for="transitionRaceStartList">PF Start Lists</label>
                    <div id="transitionFields_RaceStartList" class="transitionFields" style="display:none; margin-top: 5px;">
                        File path : <input id="transitionFile_RaceStartList" name="transitionFile_RaceStartList" style="width: 50%;">
                        Duration (secs) : <input type="number" id="transitionDuration_RaceStartList" name="transitionDuration_RaceStartList" min="1" value="10" placeholder="Duration (sec)" style="width: 10%;">
                    </div>
                </div>
                <div>
                    <input type="checkbox" id="transitionRaceResults" name="transitionOption" value="Race Results" onchange="toggleTransitionFields(TYPE_PF_RESULTS)">
                    <label for="transitionRaceResults">PF Results</label>
                    <div id="transitionFields_RaceResults" class="transitionFields" style="display:none; margin-top: 5px;">
                        File path : <input id="transitionFile_RaceResults" name="transitionFile_RaceResults" style="width: 50%;">
                        Duration (secs) : <input type="number" id="transitionDuration_RaceResults" name="transitionDuration_RaceResults" min="1" value="10" placeholder="Duration (sec)" style="width: 10%;">
                    </div>
                </div>
                <div>
                    <input type="checkbox" id="transitionPFImage" name="transitionOption" value="PFImage"  onchange="toggleTransitionFields(TYPE_PF_IMAGE)">
                    <label for="transitionPFImage">PF Images</label>
                    <div id="transitionFields_PFImage" class="transitionFields" style="display:none; margin-top: 5px;">
                        File path : <input id="transitionFile_PFImage" name="transitionFile_PFSList" style="width: 50%;">
                        Duration (secs) : <input type="number" id="transitionDuration_PFImage" name="transitionDuration_PFImage" min="1" value="10" placeholder="Duration (sec)" style="width: 10%;">
                    </div>
                </div>
                <div>
                    <input type="checkbox" id="transitionTrackStartList" name="transitionOption" value="Track Start List" onchange="toggleTransitionFields(TYPE_TRACK_STARTLIST)">
                    <label for="transitionTrackStartList">Track Start Lists</label>
                    <div id="transitionFields_TrackStartList" class="transitionFields" style="display:none; margin-top: 5px;">
                        File path : <input id="transitionFile_TrackStartList" name="transitionFile_TrackStartList" style="width: 50%;">
                        Duration (secs) : <input type="number" id="transitionDuration_TrackStartList" name="transitionDuration_TrackStartList" min="1" value="10" placeholder="Duration (sec)" style="width: 10%;">
                    </div>
                </div>
                <div>
                    <input type="checkbox" id="transitionTrackResults" name="transitionOption" value="Track Results" onchange="toggleTransitionFields(TYPE_TRACK_RESULTS)">
                    <label for="transitionTrackResults">Track Results</label>
                    <div id="transitionFields_TrackResults" class="transitionFields" style="display:none; margin-top: 5px;">
                        File path : <input id="transitionFile_TrackResults" name="transitionFile_TrackResults" style="width: 50%;">
                        Duration (secs) : <input type="number" id="transitionDuration_TrackResults" name="transitionDuration_TrackResults" min="1" value="10" placeholder="Duration (sec)" style="width: 10%;">
                    </div>
                </div>

                <div>
                    <input type="checkbox" id="transitionFieldStartList" name="transitionOption" value="Field Start List" onchange="toggleTransitionFields(TYPE_FIELD_STARTLIST)">
                    <label for="transitionFieldStartList">Field Start Lists</label>
                    <div id="transitionFields_FieldStartList" class="transitionFields" style="display:none; margin-top: 5px;">
                        File path : <input id="transitionFile_FieldStartList" name="transitionFile_FieldStartList" style="width: 50%;">
                        Duration (secs) : <input type="number" id="transitionDuration_FieldStartList" name="transitionDuration_FieldStartList" min="1" value="10" placeholder="Duration (sec)" style="width: 10%;">
                    </div>
                </div>
                <div>
                    <input type="checkbox" id="transitionFieldResults" name="transitionOption" value="Field Results" onchange="toggleTransitionFields(TYPE_FIELD_RESULTS)">
                    <label for="transitionFieldResults">Field Results</label>
                    <div id="transitionFields_FieldResults" class="transitionFields" style="display:none; margin-top: 5px;">
                        File path : <input id="transitionFile_FieldResults" name="transitionFile_FieldResults" style="width: 50%;">
                        Duration (secs) : <input type="number" id="transitionDuration_FieldResults" name="transitionDuration_FieldResults" min="1" value="10" placeholder="Duration (sec)" style="width: 10%;">
                    </div>
                </div>
                <div>
                    <input type="checkbox" id="transitionFieldResult" name="transitionOption" value="Field Result" onchange="toggleTransitionFields(TYPE_FIELD_RESULT)">
                    <label for="transitionFieldResult">Single Field Results</label>
                    <div id="transitionFields_FieldResult" class="transitionFields" style="display:none; margin-top: 5px;">
                        File path : <input id="transitionFile_FieldResult" name="transitionFile_FieldResult" style="width: 50%;">
                        Duration (secs) : <input type="number" id="transitionDuration_FieldResult" name="transitionDuration_FieldResult" min="1" value="10" placeholder="Duration (sec)" style="width: 10%;">
                    </div>
                </div>
                <div>
                    <input type="checkbox" id="transitionmessage" name="transitionOption" value="message" onchange="toggleTransitionFields(TYPE_MESSAGE)">
                    <label for="transitionmessage">Messages</label>
                    <div id="transitionFields_message" class="transitionFields" style="display:none; margin-top: 5px;">
                        File path : <input id="transitionFile_message" name="transitionFile_message" style="width: 50%;">
                        Duration (secs) : <input type="number" id="transitionDuration_message" name="transitionDuration_message" min="1" value="10" placeholder="Duration (sec)" style="width: 10%;">
                    </div>
                </div>
                <div>
                    <input type="checkbox" id="transitionImages" name="transitionOption" value="Images" onchange="toggleTransitionFields(BOARD_TYPE_IMAGES)">
                    <label for="transitionImages">Images</label>
                    <div id="transitionFields_Images" class="transitionFields" style="display:none; margin-top: 5px;">
                        File path : <input id="transitionFile_Images" name="transitionFile_Images" style="width: 50%;">
                        Duration (secs) : <input type="number" id="transitionDuration_Images" name="transitionDuration_Images" min="1" value="10" placeholder="Duration (sec)" style="width: 10%;">
                    </div>
                </div>
                <div>
                    <input type="checkbox" id="transitionvideo" name="transitionOption" value="video" onchange="toggleTransitionFields(TYPE_VIDEO)">
                    <label for="showVideos">Videos</label>
                    <div id="transitionFields_video" class="transitionFields" style="display:none; margin-top: 5px;">
                        File path : <input id="transitionFile_video" name="transitionFile_video" style="width: 50%;">
                        Duration (secs) : <input type="number" id="transitionDuration_video" name="transitionDuration_video" min="1" value="10" placeholder="Duration (sec)" style="width: 10%;">
                    </div>
                </div>
            </div>
        </fieldset>
    </div>
    <div id="tab-board">
        <fieldset>
            <legend>Save Board Configuration To</legend>
            <div style="margin-bottom: 10px;">
                <input type="checkbox" id="saveBoardToAll" name="saveBoardToAll" value="Y">
                <label for="saveBoardToAll">Save to All Boards</label>
                <button id="clearBoardSelectionBtn" style="margin-left: 15px; font-size: 12px; padding: 4px 8px;">Clear Selection</button>
                <button id="createBoardGroupBtn" style="font-size: 12px; padding: 4px 8px;">Create a Group</button>
                <!-- Compact Board Groups Section for Board Settings -->
                <div id="boardGroupsCompactSettings" style="display: flex; flex-wrap: wrap; gap: 5px;"></div>
            </div>

            <!-- Compact Board Selection for Board Settings -->
            <div id="activeBoardSelectionsSettings"></div>

            <div style="margin-top: 15px; margin-bottom:10px;">
                <button id="saveBoardBtn" style="margin-right:50px;">Save Board Information</button>
                <select id="activeBoardsSelect" onchange="activeBoards.uiIdSelected();">
                    <option value="">Select Board</option>
                </select>
                <button id="addBoardBtn" onclick="activeBoards.addBoardToList();">Add Board</button>
            </div>
        </fieldset>
        <fieldset>
            <legend>Board Configuration</legend>

            <div>
                Identifier: <input type="number" id="boardId" class="boardIdInput" disabled name="boardId">
            </div>
            <div>
                Name: <input type="text" id="boardName" class="boardNameInput" name="boardName" onchange="activeBoards.uiNameChanged();">
            </div>
            <div>
                Layout: 
                <select id="boardLayout" name="boardLayout">
                    <option value="twobyone">2x1 Layout</option>
                    <option value="threebytwo">3x2 Layout</option>
                </select>
            </div>
            <div id="singleDelayDiv" style="display:none;">
                Single Delay: <input type="number" id="singleDelay" name="singleDelay" min="3" value="3" placeholder="Single Delay (sec)" style="width: 50px;">
            </div>
            <fieldset>
                <legend>Board Types</legend>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px;">
                    <div>
                        <input type="checkbox" id="showTime" name="showOption" value="Time">
                        <label for="showTime">Clock</label>
                    </div>
                    <div>
                        <input type="checkbox" id="showRaceTime" name="showOption" value="RaceTime" onchange="displayRaceClock();">
                        <label for="showRaceTime">Race Clock</label>
                    </div>
                    <div>
                        <input type="checkbox" id="showPFList" name="showOption" value="PFSList">
                        <label for="showPFList">PF Start Lists</label>
                    </div>
                    <div>
                        <input type="checkbox" id="showPFRList" name="showOption" value="PFRList">
                        <label for="showPFRList">PF Results</label>
                    </div>
                    <div>
                        <input type="checkbox" id="showPFImage" name="showOption" value="PFImage">
                        <label for="showPFImage">PF Images</label>
                    </div>
                    <div>
                        <input type="checkbox" id="showTSList" name="showOption" value="TSList" onchange="displayEventsSet('TSList');">
                        <label for="showTSList">Track Start Lists</label>
                    </div>
                    <div>
                        <input type="checkbox" id="showTRList" name="showOption" value="TRList" onchange="displayEventsSet('TRList');">
                        <label for="showTRList">Track Results</label>
                    </div>
                    <div>
                        <input type="checkbox" id="showFSList" name="showOption" value="FSList" onchange="displayEventsSet('FSList');">
                        <label for="showFSList">Field Start Lists</label>
                    </div>
                    <div>
                        <input type="checkbox" id="showFRList" name="showOption" value="FRList" onchange="displayEventsSet('FRList');">
                        <label for="showFRList">Field Results</label>
                    </div>
                    <div>
                        <input type="checkbox" id="showFRSingle" name="showOption" value="FRSingle" onchange="displayEventsSet('FRSingle');">
                        <label for="showFRSingle">Single Field Results</label>
                    </div>
                    <div>
                        <input type="checkbox" id="showMessages" name="showOption" value="Messages">
                        <label for="showMessages">Messages</label>
                    </div>
                    <div>
                        <input type="checkbox" id="showImages" name="showOption" value="Images">
                        <label for="showImages">Images</label>
                    </div>
                    <div>
                        <input type="checkbox" id="showVideos" name="showOption" value="Videos">
                        <label for="showVideos">Videos</label>
                    </div>
                </div>
                <fieldset id="displayRaceClock" style="display:none">
                    <legend>Race Clock Configuration</legend>
                    <div>
                        <input type="number" id="raceLag" class="raceLag" name="raceLag" value="1">
                        <label for="raceLag">Race Lag (seconds)</label>
                    </div>
                    <div>
                        Show :
                        <input type="radio" id="raceFormatSeconds" name="raceFormat" value="Seconds">
                        <label for="raceFormatSeconds">Seconds</label>
                        <input type="radio" id="raceFormatTenths" name="raceFormat" value="Tenths" checked>
                        <label for="raceFormatTenths">Tenths</label>
                    </div>
                    <div>
                        <input type="checkbox" id="raceClockMessage" name="raceClockMessage" value="Y">
                        <label for="raceClockMessage">Raceclock Message</label>
                    </div>
                    <div>
                        <input type="number" id="firstMessageDelay" name="firstMessageDelay" min="1" max="60" value="3">
                        <label for="firstMessageDelay">Display First Place Message (secs)</label>
                    </div>
                </fieldset>
                <fieldset id="displaySystemConfig" style="display:none">
                    <legend>Lynx Systems</legend>
                    <div>
                        <input type="text" id="systemName" name="systemName" style="width:50%">
                        <div style="font-size: small;">
                            <p>
                        leave blank for all or multiple delimeter is a comma.
                            </p>
                        <p>
                            add ?system=XXX to the Lynx end point to use
                        </p>
                        </div>
                    </div>
                </fieldset>
                <fieldset id="displayStartListTimes" style="display:none">
                    <legend>Start List Times</legend>
                    <div>
                        <input type="checkbox" id="showStartListTimes" name="showStartListTimes" value="Y">
                        <label for="showStartListTimes">Show Start List Times</label>
                    </div>
                </fieldset>
                <fieldset id="eventSet" style="display:none">
                    <legend>Events</legend>
                    <div id="tabs">
                        <ul>
                            <li id="tab-TS" style="display:none"><a href="#tabs-TS">Track Start List</a></li>
                            <li id="tab-TR" style="display:none"><a href="#tabs-TR">Track Results</a></li>
                            <li id="tab-FS" style="display:none"><a href="#tabs-FS">Field Start List</a></li>
                            <li id="tab-FR" style="display:none"><a href="#tabs-FR">Field Results</a></li>
                            <li id="tab-FI" style=""><a href="#tabs-FI">Ignore Field Events</a></li>
                            <li id="tab-TI" style=""><a href="#tabs-TI">Ignore Track Events</a></li>
                        </ul>
                        <div id="tabs-TS">
                            <button id="showTSBtn" onclick="selectEvents('TS')">Select Events TS</button>
                            <span id="TSList"></span>
                            <div id="TSList-Events"></div>
                        </div>
                        <div id="tabs-TR">
                            <button id="showTRBtn" onclick="selectEvents('TR')">Select Events TR</button>
                            <span id="TRList"></span>
                            <div id="TRList-Events"></div>
                        </div>
                        <div id="tabs-FS">
                            <button id="showFSBtn" onclick="selectEvents('FS')">Select Events FS</button>
                            <span id="FSList"></span>
                            <div id="FSList-Events"></div>
                        </div>
                        <div id="tabs-FR">
                            <button id="showFRBtn" onclick="selectEvents('FR')">Select Events FR</button>
                            <span id="FRList"></span>
                            <div id="FRList-Events"></div>
                        </div>
                        <div id="tabs-FI">
                            <button id="showFIBtn" onclick="selectEvents('FI')">Select Field Events to Ignore</button>
                            <span id="FIList"></span>
                            <div id="FIList-Events"></div>
                        </div>
                        <div id="tabs-TI">
                            <button id="showTIBtn" onclick="selectEvents('TI')">Select Track Events to Ignore</button>
                            <span id="TIList"></span>
                            <div id="TIList-Events"></div>
                        </div>
                    </div>
                </fieldset>
            </fieldset>
        </fieldset>

    </div>
    <div id="tab-message-styles">
        <fieldset>
            <legend>Message Styles Configuration</legend>
            <div>
                Layout: 
                <select id="messageStyleLayout" name="messageStyleLayout">
                    <option value="twobyone">2x1 Layout</option>
                    <option value="threebytwo">3x2 Layout</option>
                </select>
            </div>
            
            <fieldset>
                <legend>Message Style Elements</legend>
                <div class="message-style-row">
                    <label for="styleMessageTitleStyle">Main Title:</label>
                    <input type="hidden" id="styleMessageTitleStyle" name="styleMessageTitleStyle">
                    <button type="button" id="styleMessageTitleBtn" class="styleBtn">Style</button>
                    <span id="styleMessageTitlePreview" class="style-preview">Sample Title</span>
                </div>
                
                <div class="message-style-row">
                    <label for="styleMessageRightTitleStyle">Right Title:</label>
                    <input type="hidden" id="styleMessageRightTitleStyle" name="styleMessageRightTitleStyle">
                    <button type="button" id="styleMessageRightTitleBtn" class="styleBtn">Style</button>
                    <span id="styleMessageRightTitlePreview" class="style-preview">Sample Right Title</span>
                </div>
                
                <div class="message-style-row">
                    <label for="styleMessage1Style">Message 1:</label>
                    <input type="hidden" id="styleMessage1Style" name="styleMessage1Style">
                    <button type="button" id="styleMessage1Btn" class="styleBtn">Style</button>
                    <span id="styleMessage1Preview" class="style-preview">Sample Message 1</span>
                </div>
                
                <div class="message-style-row">
                    <label for="styleMessage2Style">Message 2:</label>
                    <input type="hidden" id="styleMessage2Style" name="styleMessage2Style">
                    <button type="button" id="styleMessage2Btn" class="styleBtn">Style</button>
                    <span id="styleMessage2Preview" class="style-preview">Sample Message 2</span>
                </div>
                
                <div class="message-style-row">
                    <label for="styleMessage3Style">Message 3:</label>
                    <input type="hidden" id="styleMessage3Style" name="styleMessage3Style">
                    <button type="button" id="styleMessage3Btn" class="styleBtn">Style</button>
                    <span id="styleMessage3Preview" class="style-preview">Sample Message 3</span>
                </div>
                
                <div class="message-style-row">
                    <label for="styleMessage4Style">Message 4:</label>
                    <input type="hidden" id="styleMessage4Style" name="styleMessage4Style">
                    <button type="button" id="styleMessage4Btn" class="styleBtn">Style</button>
                    <span id="styleMessage4Preview" class="style-preview">Sample Message 4</span>
                </div>
                
                <div class="message-style-row" style="margin-top: 15px;">
                    <button id="saveMessageStylesBtn">Save Message Styles</button>
                </div>
            </fieldset>
        </fieldset>
    </div>
    <div id="tab-actions">
        <fieldset>
            <legend>Send to Boards</legend>
            <div style="margin-bottom: 10px;">
                <input type="checkbox" id="sendToAll" name="sendToAll" value="Y">
                <label for="sendToAll">Send to All Boards</label>
                <button id="clearSelectionBtn" style="margin-left: 15px; font-size: 12px; padding: 4px 8px;">Clear Selection</button>
                <button id="createGroupBtn" style="font-size: 12px; padding: 4px 8px;">Create a Group</button>
                <!-- Compact Board Groups Section -->
                <div id="boardGroupsCompact" style="display: flex; flex-wrap: wrap; gap: 5px;"></div>
            </div>
            
            <!-- Compact Board Selection -->
            <div id="activeBoardSelections"></div>
        </fieldset>
        <fieldset>
            <legend>Actions</legend>
            <button id="showRaceClockBtn">Show Race Clock</button>
            <button id="resetBtn">Reset the Clock</button>
            <button id="stopRaceClockBtn">Stop Race Clock</button>
            <button id="showClockBtn">Show Current Time</button>
            <button id="reloadBtn">Restart Screen</button>
            <button id="finishMessageBtn">Finish Message</button>
            
            <div style="margin-top: 10px;">
                <button id="sendStartTimeBtn">Send Start Time</button>
                <select id="raceStartTimeSelect" class="editable-select">
                    <option value="">Select or enter a start time</option>
                </select>
                <input type="text" id="manualStartTime" placeholder="HH:MM:SS.SS" style="margin-left: 5px;">
                <button id="addCustomTimeBtn" style="margin-left: 5px;">Add Time</button>
            </div>
        </fieldset>
        <fieldset>
            <legend>Messages</legend>
            <div id="messages">
                <div>
                    <label for="messageTitle" style="display: inline-block; width: 150px;">Main Title:</label>
                    <input type="text" id="messageTitle" class="messageInput" name="messageTitle">
                </div>
                <div>
                    <label for="messageRightTitle" style="display: inline-block; width: 150px;">Right Title:</label>
                    <input type="text" id="messageRightTitle" class="messageInput" name="messageRightTitle">
                </div>
                <div>
                    <label for="message1" style="display: inline-block; width: 150px;">Message 1:</label>
                    <input type="text" id="message1" class="messageInput" name="message1">
                </div>
                <div>
                    <label for="message2" style="display: inline-block; width: 150px;">Message 2:</label>
                    <input type="text" id="message2" class="messageInput" name="message2">
                </div>
                <div>
                    <label for="message3" style="display: inline-block; width: 150px;">Message 3:</label>
                    <input type="text" id="message3" class="messageInput" name="message3">
                </div>
                <div>
                    <label for="message4" style="display: inline-block; width: 150px;">Message 4:</label>
                    <input type="text" id="message4" class="messageInput" name="message4">
                </div>
                <div id="btMessageSelectDiv" style="display:none;">
                    <label for="btMessageSelect" style="display: inline-block; width: 150px;">Use BT Show ( Optional ):</label>
                    <select id="btMessageSelect" class="btMessageSelect" style="width: 30%;">
                        <option value="">Select a BluffTitler Show</option>
                    </select>
                    <button id="refreshBTMessageBtn">Refresh List</button>
                </div>
                <div>
                    <label for="messageDelay" style="display: inline-block; width: 150px;">Display for ( secs ):</label>
                    <input type="number" id="messageDelay" name="messageDelay" value="5" min="1" max="60">
                    <button id="setInfiniteMessageDelayBtn" onclick="document.getElementById('messageDelay').value = -1;">Set Infinite Delay</button>
                    <button id="messageBtn">Show Message</button>
                    <button id="clearMessgeBtn">Clear Message</button>
                </div>
            </div>
        </fieldset>
        <fieldset>
            <legend>Image</legend>
            <div>
                <label for="imageSelect">Select Image:</label>
                <select id="imageSelect" class="imageSelect" style="width: 30%;">
                    <option value="">Select an image</option>
                </select>
                <button id="showSelectedImageBtn">Show Selected Image</button>
                <button id="refreshImagesBtn">Refresh List</button>
            </div>
            <hr>
            <div>
                <label for="fileUpload">Or upload a new image:</label>
                <input type="file" id="fileUpload" name="fileUpload">
                <button id="uploadImageBtn">Upload Image</button>
            </div>
            <hr>
            <div>
                <label for="imageDelay">Display for ( secs ):</label>
                <input type="number" min="5" max="120" id="imageDelay" class="imageTimeInput" name="imageDelay" value="5">
                <button id="setInfiniteImageDelayBtn" onclick="document.getElementById('imageDelay').value = -1;">Set Infinite
                    Delay
                </button>
            </div>

        </fieldset>
        <fieldset>
            <legend>Video</legend>
            <div>
                <label for="videoSelect">Select Video:</label>
                <select id="videoSelect" class="videoSelect" style="width: 30%;">
                    <option value="">Select a Video</option>
                </select>
                <button id="showSelectedVideoBtn">Show Selected Video</button>
                <button id="refreshVideosBtn">Refresh List</button>
            </div>
            <div>
                <label for="btVideoSelect">Select BT Video:</label>
                <select id="btVideoSelect" class="videoSelect" style="width: 30%;">
                    <option value="">Select a BT Video</option>
                </select>
                <button id="showSelectedBTVideoBtn">Show Selected BT Video</button>
                <button id="refreshBTVideosBtn">Refresh List</button>
            </div>
            <hr>
            <label for="videoUrl">Video Url</label> : <input id="videoUrl" name="videoUrl" class="videoUrl">
            <div>
                <label for="videoDelay">Display for ( secs. -1 means infinite ):</label>
                <input type="number" min="5" max="360" id="videoDelay" class="imageTimeInput" name="videoDelay" value="-1">
                <button id="setInfiniteVideoDelayBtn" onclick="document.getElementById('videoDelay').value = -1;">Set Infinite
                    Delay
                </button>
            </div>
            <button id="showVideoBtn">Upload Video</button>
        </fieldset>
    </div>
    <div id="tab-messages">
        <fieldset>
            <legend>Messages</legend>
            <div class="ctrl-Messages">
                <div id="messageList" name="messageList"></div>
            </div>
        </fieldset>
    </div>
</div>
<div id="dialogContainer">
    <div id="eventDialog"></div>
</div>

<!-- Style Dialog (hidden by default) -->
<div id="styleDialog" title="Style Settings" style="display:none;">
    <div class="style-dialog-row">
        <label for="dialogFontSize">Font Size:</label>
        <select id="dialogFontSize">
            <option value="1em">1em</option>
            <option value="1.5em">1.5em</option>
            <option value="2em">2em</option>
            <option value="2.5em">2.5em</option>
            <option value="3em">3em</option>
            <option value="3.5em">3.5em</option>
            <option value="4em">4em</option>
            <option value="4.5em">4.5em</option>
            <option value="5em">5em</option>
            <option value="5.5em">5.5em</option>
            <option value="6em">6em</option>
            <option value="6.5em">6.5em</option>
            <option value="7em">7em</option>
            <option value="7.5em">7.5em</option>
            <option value="8em">8em</option>
            <option value="8.5em">8.5em</option>
            <option value="9em">9em</option>
            <option value="9.5em">9.5em</option>
            <option value="10em">10em</option>
            <option value="10.5em">10.5em</option>
            <option value="11em">11em</option>
            <option value="11.5em">11.5em</option>
            <option value="12em">12em</option>
            <option value="12.5em">12.5em</option>
            <option value="13em">13em</option>
            <option value="13.5em">13.5em</option>
            <option value="14em">14em</option>
            <option value="14.5em">14.5em</option>
            <option value="15em">15em</option>
        </select>
    </div>
    <div class="style-dialog-row">
        <label for="dialogFontColor">Font Color:</label>
        <input type="color" id="dialogFontColor" value="#ffffff">
    </div>
    <div class="style-dialog-row">
        <label for="dialogFontWeight">Bold:</label>
        <input type="checkbox" id="dialogFontWeight">
    </div>
    <div class="style-dialog-row">
        <label for="dialogFontStyle">Italic:</label>
        <input type="checkbox" id="dialogFontStyle">
    </div>
    <input type="hidden" id="currentStyleField">
</div>
