const STYLE_IMPORTANT = 'importantTitle';
const STYLE_EVENTNAME = 'eventName';
const STORE_RACE_START = 'raceStart';
const SINGLEATTEMPTFAILCLASS = 'singleAttemptFail';
const SINGLEATTEMPTSUCCESSCLASS = 'singleAttemptSuccess';
const SINGLEATTEMPTLATESTCLASS = ' singleAttemptLatest';
const SINGLEATTEMPTBESTCLASS = ' singleResultBestResult';
const SINGLEATTEMPTSDISPLAYED = 6;
const MILLISECONDS_IN_SECOND = 1000;
const INFINITE_MESSAGE = -1;

class MessageScheduler {
    constructor() {
        this.messages = [];
        // check queue every second
        setInterval(() => this.checkQueue(), 100);
    }

    allMessageCount() {
        return this.messages.length;
    }

    displayMessageCount() {
        let displayMessageCount = 0;
        for (const message of this.messages) {
            switch (message.ContentType) {
                case TYPE_FIELD_STARTLIST:
                case TYPE_FIELD_RESULTS:
                case TYPE_TRACK_STARTLIST:
                case TYPE_TRACK_RESULTS:
                case TYPE_IMAGE:
                case TYPE_MESSAGE:
                    displayMessageCount++;
                    break;
                default:
                    break;
            }
        }
        return displayMessageCount;
    }

    // called when a new message is received via a webSocket message
    socketMessageReceived(message) {
        if (typeof message === "string") {
            message = JSON.parse(message);
        }

        const inboundMessageKey = getMessageId(message);
        if (!inboundMessageKey) {
            return;
        }
        if (message.ContentType === TYPE_MESSAGE) {
            if ( message.resultMessage ){
                // pf first result. ignore as its manually added to queue
                return;
            }
        }
        console.log("Socket Message Received", message );
        if (message.changedResult && message.changedResult !== null && message.ContentType === TYPE_FIELD_RESULTS) {
            let singleMessage = this.createSingleMessage(message);
            this.processMessage(singleMessage);
            message.changedResult = null;
            return;
        }
        if (message.ContentType === TYPE_PF_STARTLIST) {
            let msgEventName = message.EventDetails.EventName;
            if (msgEventName !== "") {
                state.lastEventName = msgEventName;
            }
        }
        this.processMessage(message);
    }

    createSingleMessage(message) {
        let singleMessage = {
            ContentType: TYPE_FIELD_RESULT,
            EventDetails: message.EventDetails,
            RaceResults: message.Results[message.changedResult.sub],
            changedResult: message.changedResult
        }
        return singleMessage;
    }

    injectMessage(message) {
        this.messages.unshift(message);
    }

    processMessage(message) {
        try {
            if (!this.shouldMessageBeProcessed(message)) {
                return;
            }

            // every message more important than the clock, so if a message in the queue and the clock, delete the clock message
            if (this.messages.length > 0) {
                if (this.messages[0].ContentType === TYPE_LIVE_CLOCK) {
                    this.deleteTopMessage();
                }
            }
            // check again in case the clock message was deleted
            if (this.messages.length > 0) {
                if ( message.ContentType === TYPE_PF_RESULTS && this.messages[0].ContentType === TYPE_PF_STOPPED) {
                    this.deleteTopMessage();
                }
            }

            const inboundMessageId = getMessageId(message);
            // t4s_log("addMessage: " + inboundMessageKey);
            // does the inboundMessageKey already exist in the message queue
            if ( !message.transition ) {
                message.transition = true;
            }
            if ( this.messages.length === 0 ){
                state.setInProgress(false);
            }
            for (let m = 0; m < this.messages.length; m++) {
                let checkMessage = this.messages[m];
                let checkMessageId = getMessageId(checkMessage);
                if (checkMessageId === inboundMessageId && checkMessage.ContentType === message.ContentType) {
                    // same event
                    let matched = true;
                    if (checkMessage.changedResult === undefined) {
                        checkMessage.changedResult = null;
                    }
                    if (message.changedResult === undefined) {
                        message.changedResult = null;
                    }

                    if (checkMessage.changedResult === null) {
                        // got to the full result list,
                        if (message.changedResult !== null) {
                            if (m === 0) {
                                // do not want to insert into the first message
                                matched = false;
                            } else {
                                // insert message before the full list
                                this.messages.splice(m, 0, message);
                                t4s_log("processMessage: message inserted before [" + m + "]");
                                return;
                            }
                        }
                    } else {
                        matched = false;
                    }
                    if ( message.fileName && checkMessage.fileName){
                        if (message.fileName !== checkMessage.fileName) {
                            matched = false;
                        }
                    }
                    if (matched) {
                        // update stored message
                        let saveProcessed = checkMessage.processed;
                        if (message.ContentType === TYPE_PF_RESULTS) {
                            saveProcessed = false;
                            if (pfResultsListPageObj === null) {
                                pfResultsListPageObj = new PfResultsDisplay();
                            }
                            pfResultsListPageObj.checkResults(this.messages[m], message);
                        }
                        message.transition = false;
                        this.messages[m] = message;
                        this.messages[m].processed = saveProcessed;
                        t4s_log("processMessage: message already exists [" + m + "]");
                        return;
                    }
                }
            }

            if (typeof message.processed === "undefined") {
                message.processed = false
            }
            message.id = inboundMessageId;
            let replace = false;
            let messageSub = 0;
            let raceClockMessage = false;
            // if messages is Race Clock Message
            if (this.isPFMessage(message)) {
                if (this.messages.length > 0 ){
                    if ( this.messages[0].resultMessage ){
                        // top message is the Message for firt place, so check 2nd message in queue
                        let compInfo = getBoardInfo(state.getBoardId());
                        raceClockMessage = compInfo.raceClockInfo.raceClockMessage;
                        if (raceClockMessage) {
                            messageSub = 1;
                        }else{
                            messageScheduler.deleteTopMessage();
                        }
                    }
                    if (this.isPFMessage(this.messages[messageSub]) && message.ContentType !== TYPE_PF_PAUSED) {
                        replace = true;
                    }
                }
            }
            if (replace) {
                message.processed = false;
                if (messageSub === 0 || !raceClockMessage) {
                    // ignore message delay if 1 message in queue or is not the finish Clock Message
                    state.setInProgress(false);
                }
                this.messages[messageSub] = message;
            } else {
                // if message is now or important, force to top of queue
                if (this.isMessageNow(message)) {
                    let process = true;
                    if (message.ContentType === TYPE_PF_RESULTS) {
                       if ( this.messages.length > 0 && this.messages[0].ContentType === TYPE_PF_IMAGE) {
                            if ( getMessageId(message) === getMessageId(this.messages[0])) {
                                // insert message after image
                                this.messages.splice(1, 0, message);
                                process = false;
                            }
                        }
                    }
                    if (process) {
                        this.messages.unshift(message);
                        // force action
                        state.pageComplete(null, 0);
                    }
                } else {
                    this.messages.push(message);
                }
            }
            t4s_log("addMessage: message queue : " + this.messages.length);
        } catch (error) {
            console.error("Error processing message:", error);
            // Graceful recovery logic
        }
    }

    isPFMessage(message) {
        if (message.ContentType === TYPE_PF_RESULTS || message.ContentType === TYPE_PF_START || message.ContentType === TYPE_PF_PAUSED || message.ContentType === TYPE_PF_STOPPED || message.ContentType === TYPE_PF_RESET || message.ContentType === TYPE_WIND) {
        // if (message.ContentType === TYPE_PF_MANUAL_START ||message.ContentType === TYPE_PF_START || message.ContentType === TYPE_PF_PAUSED || message.ContentType === TYPE_PF_STOPPED || message.ContentType === TYPE_PF_RESET || message.ContentType === TYPE_WIND) {
            return true;
        }
        return false;
    }
    closeMessage() {
        state.pageComplete(null, 0);
    }

    shouldMessageBeProcessed(payloadObj) {
        const actions = {
            [TYPE_CLOSE_MESSAGE]: () => {
                this.closeMessage();
                return false;
            },
            [TYPE_PF_START]: () => {
                return showRaceClockPayload();
            },
            [TYPE_PF_MANUAL_START]: () => {
                return showRaceClockPayload();
            },
            [TYPE_WIND]: () => {
                return showRaceClockPayload() && state.raceClock.isClockRunning();
            },
            [TYPE_PF_PAUSED]: () => {
                return showRaceClockPayload() && state.raceClock.isClockRunning();
            },
            [TYPE_RACE_CLOCK]: () => {
                return showRaceClockPayload();
            },
            [TYPE_PF_STOPPED]: () => {
                return true;
            },
            [TYPE_PF_RESET]: () => {
                return true;
            },
            [TYPE_COMPINFO]: () => {
                return true;
            },
            [TYPE_RACE_RELOAD]: () => {
                location.reload();
                return false;
            },
            [TYPE_LIVE_CLOCK]: () => {
                return showClockPayload();
            },
            [TYPE_FIELD_STARTLIST]: () => {
                return showFieldStartListPayload(payloadObj);
            },
            [TYPE_TRACK_STARTLIST]: () => {
                return showTrackStartListPayload(payloadObj);
            },
            [TYPE_PF_STARTLIST]: () => {
                return showPhotoFinishStartListPayload();
            },
            [TYPE_PF_RESULTS]: () => {
                return showPhotoFinishResultsPayload();
            },
            [TYPE_PF_SAVE_START]: () => {
                if (payloadObj.startTime) {
                    state.raceClock.setClockStart(payloadObj.startTime);
                    // Restart the race clock with the new start time
                    location.reload();
                }
                return false;
            },
            [TYPE_PF_IMAGE]: () => {
                return showPhotoFinishImagePayload();
            },
            [TYPE_FIELD_RESULTS]: () => {
                return showFieldResultsPayload(payloadObj);
            },
            [TYPE_FIELD_RESULT]: () => {
                return showFieldResultPayload(payloadObj);
            },
            [TYPE_TRACK_RESULTS]: () => {
                return showTrackResultsPayload(payloadObj);
            },
            [TYPE_MESSAGE]: () => {
                return showMessages();
            },
            [TYPE_IMAGE]: () => {
                return showImages();
            },
            [TYPE_VIDEO]: () => {
                return showVideos();
            },
            [TYPE_CANCEL_MESSAGE]: () => {
                return true;
            },
            [TYPE_BOARD_INFO]: () => {
                checkBoardInfo(payloadObj);
                return false;
            },
            [TYPE_MESSAGE_STYLES]: () => {
                // Save message styles to localStorage
                if (payloadObj.messageStyles) {
                    t4s_log("Received message styles: " + JSON.stringify(payloadObj.messageStyles));
                    localStorage.setItem("messageStyles", JSON.stringify(payloadObj.messageStyles));
                    // Update state with the new styles
                    state.messageStyles = payloadObj.messageStyles;
                }
                return false; // No need to display anything
            },
            default: () => {
                return false;
            }
        };
        // if does not have ContentType
// boardId of 0 is a global message
        if (payloadObj.boardId ){
            let globalMsg = false;
            if (Array.isArray(payloadObj.boardId)) {
                if (payloadObj.boardId[0] === ALL_BOARDS_TEXT) {
                    globalMsg = true;
                }
            }else{
                if (payloadObj.boardId === ALL_BOARDS_TEXT) {
                    globalMsg = true;
                }
            }
            if (!globalMsg) {
                let thisBoardId = "" + state.getBoardId();
                // if boardId is an array, check if this board is in the array
                if (Array.isArray(payloadObj.boardId)) {
                    if (!payloadObj.boardId.includes(thisBoardId)) {
                        return false;
                    }
                } else {
                    if (payloadObj.boardId !== parseInt(thisBoardId)) {
                        return false;
                    }
                }
            }
        }
        if ( payloadObj.systemId && payloadObj.systemId !== "") {
            if ( state.systems ){
                if ( !state.systems.includes(ALL_BOARDS_TEXT) ) {
                    if (!state.systems.includes(payloadObj.systemId.toLowerCase())) {
                        return false;
                    }
                }
            }
        }

        return (actions[payloadObj.ContentType] || actions.default)();
    }

    progressDisplay(text = "") {
        if (text === "") {
            text = this.allMessageCount() + "/" + this.displayMessageCount();
        }
        $("#inProgress").text(text);
    }

    isMessageNow(message) {
        let retVal = false;
        // if ContentType is a type that needs actioning NOW
        if (this.isPFMessage(message)) {
            retVal = true;
        }
        if (message.ContentType === TYPE_RACE_RELOAD) {
            retVal = true;
        }
        if (message.ContentType === TYPE_CANCEL_MESSAGE) {
            retVal = true;
        }
        if (message.ContentType === TYPE_PF_RESULTS) {
            retVal = true;
        }
        if (message.ContentType === TYPE_PF_IMAGE) {
            retVal = true;
        }
        if (message.ContentType === TYPE_PF_RESET) {
            retVal = true;
        }
        return retVal;
    }

    loopMessage(topMessage) {
        let maxLoopCount = 2;
        let loop = false;
        let showNext = false;
        let showNextMethod = null;
        if (topMessage.ContentType === TYPE_TRACK_RESULTS) {
            loop = true;
            showNext = showClockPayload();
            showNextMethod = addLiveClock;
        }
        if (topMessage.ContentType === TYPE_PF_STARTLIST) {
            loop = true;
            showNext = showRaceClockPayload();
            showNextMethod = raceReset;
        }
        if ( loop ){
            topMessage.loopCount = topMessage.loopCount || 1;
            if (!showNext) {
                // pointless checking, cant show next
                topMessage.processed = false;
            } else {
                if (topMessage.loopCount <= maxLoopCount || showNextMethod === null) {
                    // topMessage.interupt = true; ???
                    topMessage.processed = false;
                } else if (topMessage.loopCount === (maxLoopCount + 1) ) {
                    if ( pageListDisplayObj ) {
                        pageListDisplayObj.clearTimeouts();
                        pageListDisplayObj = null;
                    }
                    this.deleteTopMessage();
                    showNextMethod();
                }
            }
        }
    }

    checkQueue() {
        if (this.messages.length > 0) {
            let topMessage = this.messages[0];
            let interupt = false;
            if (this.messages.length > 1) {
                interupt = topMessage.interupt || false;
                if (interupt) {
                    topMessage.processed = true;
                }
            } else if (topMessage.processed) {
                // check if message can loop
                this.loopMessage(topMessage);
            }
            if (topMessage.processed === false) {
                if ( this.isPFMessage(topMessage)){
                    interupt = true;
                }
            }
            if (interupt) {
                // top message is interuptable
                state.setInProgress(false);
                clearTimeout(state.windowTimeout);
            }
            // top message has been processed and finished
            if (!state.getIsDisplayInProgress() && topMessage.processed) {
                let removeTopMessage = true;

                if ( this.messages.length === 1 ){
                    // Only 1 message, check if leave it on screen
                    if (this.isPFMessage(topMessage)) {
                        removeTopMessage = false;
                    }
                    if ( topMessage.ContentType === TYPE_LIVE_CLOCK ){
                        // removing this will only add another
                        removeTopMessage = false;
                    }
                    if ( topMessage.delay === INFINITE_MESSAGE ){
                        // infinite video
                        removeTopMessage = false;
                    }
                }
                if ( removeTopMessage) {
                    pfResultsListPageObj = null;
                    // delete top message from this.messages
                    this.deleteTopMessage();
                }
            }
        }

        this.showTopMessage();
    }

    deleteTopMessage() {
        // Clear any active timeouts if there's a pageListDisplayObj
        if (pageListDisplayObj) {
            pageListDisplayObj.clearTimeouts();
        }
        
        // delete top message from this.messages
        this.messages.shift();
    }

    showTopMessage() {

        let message = null;

        if (this.messages.length > 0) {
            message = this.messages[0];
            let currentType = message.ContentType;
            if (currentType === TYPE_PF_RESULTS) {
                // for PF results to display as the results have been updated, so processed will have been reset
                state.setInProgress(this.messages[0].processed);
            }
        }
        if (!state.getIsDisplayInProgress()) {
            if (message !== null) {
                processSocketMessage(message);
            } else {
                // no messages, add the Live clock
                addLiveClock();
            }
        }
    }
}

let state = {
    debug: false,
    currentEventNumber: 0,
    lastEventName: "",
    windowTimeout: null,
    boardId: 0,
    boardType: [],
    hasBootVideoPlayed: false,
    events: [],
    activeBoards: [],
    displayEvents: [], // events that should be montiored for display
    raceClockInfo: {},
    transitions: [],
    isDisplayInProgress: false,
    messageStyles: null,
    compInfo: null,
    setInProgress: function (inProgress) {
        this.isDisplayInProgress = inProgress;

        // no messages means the boot video has been called
        if (messageScheduler.allMessageCount() > 0) {
            // update the progress display
            if (inProgress) {
                messageScheduler.progressDisplay(messageScheduler.messages[0].StartListId)
            } else {
                messageScheduler.progressDisplay();
            }
        }
    },
    getIsDisplayInProgress: function () {
        return this.isDisplayInProgress;
    },
    getBoardId: function () {
        let boardId = sessionStorage.getItem('boardId');
        if (!boardId) {
            boardId = Math.floor(1000 + Math.random() * 9000);
        } else {
            boardId = parseInt(boardId);
        }
        this.setBoardId(boardId);
        return boardId;
    },
    setBoardId: function (boardId) {
        state.boardId = boardId;
        sessionStorage.setItem('boardId', boardId);
    },
    pageComplete: function (callback = null, delay = PROP_DELAY_DISPLAY) {
        if ( messageScheduler.messages.length > 0) {
            let topMessage = messageScheduler.messages[0];
            if (topMessage.loopCount) {
                topMessage.loopCount++;
            } else {
                topMessage.loopCount = 1;
            }
        }
        if (delay < 0) {
            // infinite
            return;
        }
        if (delay < MILLISECONDS_IN_SECOND) {
            // under 1000ms, assume seconds
            delay = delay * MILLISECONDS_IN_SECOND;
        }
        clearTimeout(this.windowTimeout);
        this.windowTimeout = window.setTimeout(function () {
            state.setInProgress(false);
            if (callback !== null) {
                callback();
            }
        }, delay);
    },
    raceClock: {
        hours: 0,
        minutes: 0,
        seconds: 0,
        tenths: 0,
        clockStoppedTime: "",
        interval: null,
        reset: function (complete = true) {
            this.stop(complete);
            this.hours = 0;
            this.minutes = 0;
            this.seconds = 0;
            this.tenths = 0;
            this.clockStoppedTime = "";
            this.display();
            state.setInProgress(false);
            this.clearRaceClockStore();
            $("#windReading").text("");
            $("#lapWindReading").text("");
        },
        getStoppedDelay: function (){
            let total = ((this.hours * 3600) + (this.minutes * 60 ) + this.seconds);
            total = total / 4;
            if ( total < 5 ){
                return 5;
            }
            if ( total > 20){
                return 20;
            }
            return total;
        },
        clearRaceClockStore: function () {
            localStorage.removeItem(STORE_RACE_START);
        },
        getLastRaceClockStart: function () {
            return localStorage.getItem(STORE_RACE_START);
        },
        checkClockStart: function () {
            let lastStartTime = this.getLastRaceClockStart();
            if (lastStartTime === null) {
                return false;
            }
            state.raceClock.setClockStart(lastStartTime);
            state.raceClock.start(false);
            return true;
        },
        setClockStart: function (clockStart) {
            const now = moment();
            const today = now.format("DD-MM-yyyy");
            let startTime = moment(today + " " + clockStart, "DD-MM-yyyy HH:mm:ss.SS");
            // remove now from adjustedTime
            const diff = now.diff(startTime);
            const raceClock = moment(diff).format("HH:mm:ss:S");
            let arr = raceClock.split(":");
            state.raceClock.hours = parseInt(arr[0]) - 1;
            state.raceClock.minutes = parseInt(arr[1]);
            state.raceClock.seconds = parseInt(arr[2]);
            state.raceClock.tenths = parseInt(arr[3]);
            return true;
        },
        addAnyLag: function(messageObj){
            if ( !state.raceClockInfo ){
                return;
            }
            this.seconds += state.raceClockInfo.raceLag;
            if ( this.seconds > 59 ){
                this.minutes++;
                this.seconds -= 60;
            }
            return;
        },
        start: function (reset = true, messageObj = null) {
            if (reset) {
                this.reset(false);
            }

            let lastStartTime = this.getLastRaceClockStart();
            if (lastStartTime === null) {
                // save the start incase of any issues/ reload
                const now = moment();
                localStorage.setItem(STORE_RACE_START, now.format("HH:mm:ss.SS"));
            }
            this.addAnyLag(messageObj);
            // clear this.interval if it is already running
            clearInterval(this.interval);
            this.interval = setInterval(() => {
                this.tenths++;
                if (this.tenths === 10) {
                    this.tenths = 0;
                    this.seconds++;
                }
                if (this.seconds === 60) {
                    this.seconds = 0;
                    this.minutes++;
                }
                if (this.minutes === 60) {
                    this.minutes = 0;
                    this.hours++;
                }
                this.display();
            }, 100);
        },
        stop: function (complete = true, beamTime = "") {
            clearInterval(this.interval);
            this.clearRaceClockStore();
            if ( beamTime !== "" ) {
                this.clockStoppedTime = beamTime;
            }
            if (complete) {
                state.pageComplete(null, PROP_DELAY_STOPPED_RACECLOCK);
            }
        },
        display: function () {
            let obj = $("[" + PROP_RACECLOCK + "]");
            if (obj.length > 0) {
                obj.text(this.getRaceClockDisplay());
            }
        },
        getRaceClockDisplay: function () {
            let time = "";
            if ( this.clockStoppedTime !== "" ){
                return this.clockStoppedTime;
            }

            if (this.hours > 0) {
                time += this.hours + ":";
            }
            if (this.hours > 0 || this.minutes > 0) {
                if (this.minutes < 10) {
                    time += "0";
                }
                time += this.minutes + ":";
            }
            if ((this.hours > 0 || this.minutes > 0) && this.seconds < 10) {
                time += "0";
            }
            time += this.seconds;
            if ( state.raceClockInfo.raceFormat === "Seconds" || this.hours > 0 ){
                return time;
            }
            time += "." + this.tenths;
            return time;
        },
        isClockRunning: function () {
            return state.raceClock.hours > 0 || state.raceClock.minutes > 0 || state.raceClock.seconds > 0 || state.raceClock.tenths > 0;
        }
    },
    currentTime: {
        interval: null,
        display: function () {
            clearInterval(this.interval);
            this.interval = setInterval(() => {
                let time = moment().format('HH:mm:ss');
                let obj = $("[" + PROP_LIVECLOCK + "]");
                if (obj.length > 0) {
                    obj.text(time);
                }
            }, 10);
        }
    },
    getCompName: function () {
        if ( state.compInfo && state.compInfo.compName) {
            return state.compInfo.compName;
        }
        return "Welcome";
    },
    loadMessageStyles: function() {
        const savedStyles = localStorage.getItem("messageStyles");
        if (savedStyles) {
            try {
                this.messageStyles = JSON.parse(savedStyles);
                t4s_log("Loaded message styles: " + JSON.stringify(this.messageStyles));
            } catch (e) {
                console.error("Error parsing message styles:", e);
                this.messageStyles = {};
            }
        } else {
            this.messageStyles = {};
        }
        return this.messageStyles;
    }
}
let activeTimeout = null;

const messageScheduler = new MessageScheduler();

function boardReady(compInfo) {
    state.compInfo = compInfo;
    loadBoardLayout();
    getBoardIdAndInfo();
    // Load message styles
    state.loadMessageStyles();
    t4s_log("Board initialized with message styles: " + JSON.stringify(state.messageStyles));

    // Check if a race was in progress when page was refreshed
    if (state.raceClock.checkClockStart()) {
        // Race was in progress, display the race clock
        if ( messageScheduler.messages.length > 0 ){
            messageScheduler.deleteTopMessage();
        }
        let message = {
            ContentType: TYPE_RACE_CLOCK
        }
        messageScheduler.injectMessage(message);
        window.setTimeout(function(){
            displayRaceClock();
        }, 100);
    }
}
function getBoardIdAndInfo() {
    if (state.boardId === 0) {
        state.getBoardId();
    }

    let boardInfo = getBoardInfo(state.boardId);
    state.boardType = boardInfo.boardType;
    state.displayEvents = boardInfo.events || [];
    state.raceClockInfo = boardInfo.raceClockInfo || {};
    state.transitions = boardInfo.transitions || [];
    state.systems = boardInfo.systemName || "";
    if ( state.systems !== "" ){
        state.systems = state.systems.split(",").map(system => (system.trim()).toLowerCase());
    }else{
        state.systems = ALL_BOARDS;
    }
}

function sendBoardActive() {
    // cancel activeTimeout
    clearTimeout(activeTimeout);
    let waitForSocket = true;
    console.log("Trying to send board active message");
    if ( webSocket !== null ) {
        if (webSocket.readyState === webSocket.OPEN) {
            waitForSocket = false;
            let message = getBaseMessage(TYPE_BOARD_ACTIVE);
            message.boardId = state.getBoardId();
            sendBoardMessage(message);
        }
    }
    if (waitForSocket)  {
        // wait for a second and try again
        activeTimeout = setTimeout(sendBoardActive, 1000);
    }
}

function showClockPayload() {
    if (state.boardType.length === 0) {
        return true;
    }
    return state.boardType.includes(BOARD_TYPE_CLOCK);
}

function showRaceClockPayload() {
    if (state.boardType.length === 0) {
        return true;
    }
    return state.boardType.includes(BOARD_TYPE_RACECLOCK);
}

function showEventId(type, eventDetails) {
    let events = state.displayEvents[type] || [];
    let heatNo = eventDetails.heatNo || eventDetails.HeatNumber || 0;
    let eventId = eventDetails.EventNumber || eventDetails.EventId || 0;
    let key = eventId + "-" + heatNo;

    if (events.length > 0) {
        // Board is set to only show certain events
        if (events.includes(key)) {
            return true;
        }
        return false;
    }

    // board have ignored events ?
    let ignore = type[0] + "I";
    events = state.displayEvents[ignore] || [];
    if (events.length > 0) {
        if (events.includes(key)) {
            return false;
        }
    }
    return true;
}

function showFieldStartListPayload(payloadObj) {
    let retVal = false;
    if (state.boardType.includes(BOARD_TYPE_FSLISTS)) {
        retVal = showEventId(EVENT_TYPE_FS, payloadObj.EventDetails);
    }
    return retVal;
}

function showFieldResultsPayload(payloadObj) {
    let retVal = false;
    if (state.boardType.includes(BOARD_TYPE_FRLISTS)) {
        retVal = showEventId(EVENT_TYPE_FR, payloadObj.EventDetails);
    }
    return retVal;
}

function showFieldResultPayload(payloadObj) {
    let retVal = false;
    if (state.boardType.includes(BOARD_TYPE_FRSINGLE)) {
        retVal = showEventId(EVENT_TYPE_FR, payloadObj.EventDetails);
    }
    return retVal;
}

function showTrackStartListPayload(payloadObj) {
    let retVal = false;
    if (state.boardType.includes(BOARD_TYPE_TSLISTS)) {
        retVal = showEventId(EVENT_TYPE_TS, payloadObj.EventDetails);
    }

    return retVal;
}

function showTrackResultsPayload(payloadObj) {
    let retVal = false;
    if (state.boardType.includes(BOARD_TYPE_TRLISTS)) {
        retVal = showEventId(EVENT_TYPE_TR, payloadObj.EventDetails);
    }

    return retVal;
}

function showPhotoFinishStartListPayload() {
    let retVal = state.boardType.includes(BOARD_TYPE_PFSLISTS);
    return retVal;
}

function showPhotoFinishResultsPayload() {
    let retVal = state.boardType.includes(BOARD_TYPE_PFRLISTS);
    return retVal;
}

function showPhotoFinishImagePayload() {
    let retVal = state.boardType.includes(BOARD_TYPE_PFIMAGE);
    return retVal;
}

function showMessages() {
    let retVal = state.boardType.includes(BOARD_TYPE_MESSAGES);
    return retVal;
}

function showImages() {
    let retVal = state.boardType.includes(BOARD_TYPE_IMAGES);
    return retVal;
}

function showVideos() {
    let retVal = state.boardType.includes(BOARD_TYPE_VIDEOS);
    return retVal;
}

function getMessageId(message, asString = true) {
    let messageId = "";
    if (message.EventDetails) {
        if (asString) {
            messageId = JSON.stringify(message.EventDetails);
        } else {
            messageId = message.EventDetails;
        }
    } else if (message.MessageTime) {
        messageId = message.MessageTime;
    } else if (message.ContentType) {
        messageId = message.ContentType;
    } else if (message.status) {
        messageId = message.status
    }
    return messageId;
}
function getTransition(type) {
    type = type.replace(/\s+/g, '');
    let transition = state.transitions[type];
    if (!transition) {
        transition = {
            enabled: false,
            file: ""
        };
    }
    return transition;
}
function processSocketMessage(payload) {
    try {
        let payloadObj = typeof payload === "string" ? JSON.parse(payload) : payload;

        // For message type, log styles before displaying
        if (payloadObj.ContentType === TYPE_MESSAGE) {
            t4s_log("Processing message type message");
            logMessageStyles();
        }

        if (state.boardId === 0) {
            getBoardIdAndInfo();
        }

        if ( payloadObj.transition ){
            // get transition info for payload.ContentType
            let transition = getTransition(payloadObj.ContentType);
            if ( transition.file !== "") {
                payloadObj.transition = false;
                let message = {
                    "ContentType": TYPE_VIDEO,
                    "url": transition.file,
                    "delay": transition.duration,
                    "controller": true,
                    "transition": false,
                    "boardId": state.getBoardId()
                }

                messageScheduler.injectMessage(message);
                return;
            }
        }
        payloadObj.processed = true;
        eventStore.storeEvent(payloadObj);
        t4s_log("processSocketMessage: " + payloadObj.ContentType);

        const actions = {
            [TYPE_PF_MANUAL_START]: () => {
                state.raceClock.setClockStart("00:01:00.00");
                displayRaceClock();
            },
            [TYPE_PF_START]: () => {
                startRaceClock(payloadObj);
                displayRaceClock();
            },
            [TYPE_PF_PAUSED]: () => {
                displayLaptime(payloadObj);
            },
            [TYPE_WIND]: () => {
                displayWindReading(payloadObj);
            },
            [TYPE_RACE_CLOCK]: () => {
                displayRaceClock();
            },
            [TYPE_PF_STOPPED]: () => {
                raceStopped(payloadObj.RaceBeamTime, true);
            },
            [TYPE_PF_RESET]: () => {
                raceReset();
            },
            [TYPE_COMPINFO]: () => {
                state.compInfo = payloadObj.config;
                loadBoardLayout();
                sendBoardActive();
                displayClock();
            },
            [TYPE_RACE_RELOAD]: () => {
                // don't think would ever be called here, already processed
                location.reload();
            },
            [TYPE_LIVE_CLOCK]: () => {
                displayClock();
            },
            [TYPE_FIELD_STARTLIST]: () => {
                displayStartList(payloadObj);
            },
            [TYPE_TRACK_STARTLIST]: () => {
                displayStartList(payloadObj);
            },
            [TYPE_PF_STARTLIST]: () => {
                displayStartList(payloadObj);
            },
            [TYPE_PF_RESULTS]: () => {
                displayResultsList(payloadObj);
            },
            [TYPE_PF_IMAGE]: () => {
                displayResultsImage(payloadObj);
            },
            [TYPE_FIELD_RESULT]: () => {
                displaySingleResult(payloadObj);
            },
            [TYPE_FIELD_RESULTS]: () => {
                displayOfficialResultsList(payloadObj);
            },
            [TYPE_TRACK_RESULTS]: () => {
                displayOfficialResultsList(payloadObj);
            },
            [TYPE_MESSAGE]: () => {
                displayMessage(payloadObj);
            },
            [TYPE_IMAGE]: () => {
                displayImage(payloadObj);
            },
            [TYPE_VIDEO]: () => {
                displayVideo(payloadObj);
            },
            [TYPE_CANCEL_MESSAGE]: () => {
                cancelMessage();
            },
            default: () => {
                if (state.raceClock.checkClockStart()) {
                    // rebooted during a race
                    displayRaceClock();
                } else {
                    if (showClockPayload()) {
                        displayClock();
                    }
                }
            }
        };

        (actions[payloadObj.ContentType] || actions.default)();
    } catch (error) {
        t4s_log("Error processing socket message: " + error.message);
        // Consider adding fallback behavior
    }
}

function checkBoardInfo(payloadObj) {
    let boardId = state.boardId;
    t4s_log("checkBoardInfo: " + boardId + " : " + state.getBoardId());
    if (boardId !== state.getBoardId()) {
        return;
    }
    state.boardId = boardId;
    state.boardType = payloadObj.boardType;
    state.displayEvents = payloadObj.events;
    state.raceClockInfo = payloadObj.raceClockInfo;
    state.transitions = payloadObj.transitions;
    state.systems = payloadObj.systemName;
    state.brandingStyle = payloadObj.brandingStyle; // Store branding style in state

    if (state.systems !== "") {
        state.systems = state.systems.split(",").map(system => (system.trim()).toLowerCase());
    } else {
        state.systems = ALL_BOARDS;
    }

    // Check if layout has changed and reload if necessary
    const currentLayout = document.getElementById("layoutStylesheet").href.split('/').pop().replace('.css', '');
    if (payloadObj.boardLayout && currentLayout !== payloadObj.boardLayout) {
        document.getElementById("layoutStylesheet").href = "/" + payloadObj.boardLayout + ".css";
    }

    // Check if branding style has changed and update if necessary
    if (payloadObj.brandingStyle) {
        let brandingLink = document.getElementById("brandingStylesheet");
        if (!brandingLink) {
            brandingLink = document.createElement("link");
            brandingLink.id = "brandingStylesheet";
            brandingLink.rel = "stylesheet";
            brandingLink.type = "text/css";
            document.head.insertBefore(brandingLink, document.getElementById("layoutStylesheet"));
        }
        brandingLink.href = "/brand/" + payloadObj.brandingStyle + ".css";
    }

    delete payloadObj.ContentType;
    delete payloadObj.controller;
    saveBoardInfo(payloadObj);
}

function displayVideo(payloadObj = {}) {
    let url = payloadObj.url;
    // if ( https://www.youtube.com/watch?v=HWrrUPXLLQE)
    // if url is a youtube link, convert to embed
    if (url.includes("youtube.com/watch?v=")) {
        url = url.replace("watch?v=", "embed/");
        // mute=1 allows autoplay is a browser restriction
        url += "?autoplay=1&mute=1";
    }

    displayElement(ELEMENT_VIDEO);

    if (url.endsWith(".mp4")) {
        if (url.startsWith("loop") || payloadObj.delay === INFINITE_MESSAGE) {
            $('#video').html('<video autoplay muted loop playsinline name="media" class="video"><source src="' + url + '" type="video/mp4"></video>');
        }else {
            $('#video').html('<video autoplay muted playsinline name="media" class="video"><source src="' + url + '" type="video/mp4"></video>');
        }
    } else {
        $('#video').html('<iframe style="width: 100%;height: 99%;background-color: white;" src="' + url + '" allow="autoplay"></iframe>');
    }

    // if delay is zero, play who video once (
    if ( payloadObj.delay > INFINITE_MESSAGE) {
        if ( payloadObj.delay === 0 ) {
            payloadObj.delay = INFINITE_MESSAGE;
        }
        state.pageComplete(cancelMessage, payloadObj.delay);
    }
}

function displayBootVideo() {
    let obj = {
        url: '/t4s.mp4',
        delay: 5000
    }
    displayVideo(obj);
}

function addLiveClock() {
    if (!showClockPayload()) {
        // board not showing clock, so dont bother
        return;
    }
    let msg = getBaseMessage(TYPE_LIVE_CLOCK)
    msg.interupt = true;
    messageScheduler.socketMessageReceived(msg);
}
function useFadeForMessage(){
    const topMessage = messageScheduler.messages[0];
    if ( topMessage ) {
        if (topMessage.ContentType === TYPE_PF_START) {
            return false;
        }
        if (topMessage.ContentType === TYPE_PF_STOPPED) {
            return false;
        }
    }
    return true;
}
function clearScreen(toObject) {
    if ( useFadeForMessage() ) {
        $("[t4s-element]").fadeOut();
        toObject.fadeIn();
    }else{
        $("[t4s-element]").hide();
        toObject.show();
    }
}

function cancelMessage() {
    $('#video').html('');
    if (messageScheduler.messages.length > 1 && messageScheduler.messages[0].ContentType[0] === TYPE_CANCEL_PREFIX) {
        messageScheduler.messages.splice(1, 1);
    }
}

function setClockObj(id, type = PROP_RACECLOCK) {
    // change which obj is the clock of type
    let obj = $("[" + type + "]");
    if (obj.length > 0) {
        obj.removeAttr(type);
    }
    obj = $("#" + id);
    if (obj.length > 0) {
        obj.attr(type, true);
    }
}

function displayElement(element) {
    t4s_log("displayElement: " + element);
    if ( !state.currentDisplayed || state.currentDisplayed !== element ){
        state.currentDisplayed = element;
        let key = "[t4s-element=" + element + "]";
        clearScreen($(key));
    }

    state.setInProgress(true);
}

function raceReset() {
    state.raceClock.reset();
    if (showRaceClockPayload() ){
        displayRaceClock();
    }else{
        // remove the race reset message
        messageScheduler.deleteTopMessage();
    }
}

function raceStopped(raceTime, complete = false) {
    if (complete) {
        // if top message is PF_STOPPED, mark inprogress
        if (messageScheduler.messages.length > 0) {
            let topMessage = messageScheduler.messages[0];
            if (topMessage.ContentType === TYPE_PF_STOPPED) {
                state.setInProgress(true);
                topMessage.processed = true;
            }
        }
    }
    state.raceClock.clearRaceClockStore();
    if (showRaceClockPayload() ) {
        t4s_log("raceStopped");
        displayEventName();
        setClockObj(ELEMENT_LAP_RACECLOCK, PROP_RACECLOCK);
        $("#lapTime").text(raceTime);
        if (!state.raceClock.isClockRunning()) {
            $("#lapRaceClock").text("");
        }
        displayElement(ELEMENT_LAP_CLOCK);
        let delay = state.raceClock.getStoppedDelay();
        state.pageComplete(null, delay);
    }
}

function finishLapTime() {
    messageScheduler.deleteTopMessage();
    displayRaceClock();
}

function displayLaptime(payloadObj) {
    t4s_log("displayLaptime");
    displayEventName();
    setClockObj(ELEMENT_LAP_RACECLOCK, PROP_RACECLOCK);
    let lapTime = state.raceClock.getRaceClockDisplay();
    if (payloadObj.RaceBeamTime) {
        lapTime = payloadObj.RaceBeamTime;
    }
    $("#lapTime").text(lapTime);
    displayElement(ELEMENT_LAP_CLOCK);
    state.pageComplete(finishLapTime);
}
function displayWindReading(payloadObj) {
    t4s_log("displayWindReading");
    let wind = payloadObj.WindReading;

    $("#windReading").text(wind);
    $("#lapWindReading").text(wind);
    // displayElement(ELEMENT_WIND_CLOCK);
    // state.pageComplete(finishLapTime);
}

function displayClock() {
    displayCompName();
    displayFooter();
    if (!state.hasBootVideoPlayed) {
        // displayBootVideo();
        state.hasBootVideoPlayed = true;
    } else {
        t4s_log("displayClock");

        setClockObj(ELEMENT_LIVE_CLOCK, PROP_LIVECLOCK);
        state.currentTime.display();
        displayElement(ELEMENT_LIVE_CLOCK);
        state.pageComplete();
    }
}

function startRaceClock(payloadObj) {
    // Use server-provided start time if available, otherwise fall back to current time
    if (payloadObj.serverStartTime) {
        state.raceClock.setClockStart(payloadObj.serverStartTime);
    } else {
        // Fallback to current behavior
        const now = moment();
        state.raceClock.setClockStart(now.format("HH:mm:ss.SS"));
    }
    $("#raceClockTitle").text(payloadObj.EventDetails.EventName);
    state.raceClock.start(true, payloadObj);
}

function displayRaceClock() {
    displayEventName("-",false);
    setClockObj(ELEMENT_RACE_CLOCK);
    displayElement(ELEMENT_RACE_CLOCK);
    state.raceClock.display();
}

function displayCompName() {
    let useName = state.getCompName();
    displayTitle(useName, "");
}

function displayEventName(rightTitle = "", getLastEvent = false) {
    let leftTitle = state.getCompName();
    let currentEventName = eventStore.getEventName();
    if (currentEventName !== "") {
        if (rightTitle !== "-") {
            leftTitle = currentEventName;
        }else{
            rightTitle = "";
        }
    }else if (getLastEvent) {
        // set from last race start list
        leftTitle = state.lastEventName;
    }
    displayTitle(leftTitle, rightTitle);
}

function displayTitle(leftTitle, rightTitle = "", className = STYLE_EVENTNAME) {
    let titleObj = $("[eventname]:visible");
    if (className === STYLE_EVENTNAME) {
        titleObj.addClass(STYLE_EVENTNAME);
        titleObj.removeClass(STYLE_IMPORTANT);
    } else {
        titleObj.removeClass(STYLE_EVENTNAME);
        titleObj.addClass(STYLE_IMPORTANT);
    }
    // clear any style from messages
    titleObj.prop("style", "");
    titleObj.text(leftTitle);

    $("#pageTitle").text(rightTitle);
    displayFooter();
}

function displayFooter() {
    let footerObj = $("#footer");
    footerObj.text("Tech4Sports - \u00A9 " + moment().format('YYYY'));
    let boardId = state.getBoardId();
    footerObj = $("#boardId");
    footerObj.text(boardId);
}

function displayImage(payload) {
    let imageElement = $("#image");

    if (payload.image) {
        // Handle base64 encoded image data
        imageElement.attr("src", payload.image);
    } else if (payload.imagePath) {
        // Handle image path
        console.log("Displaying image from path:", payload.imagePath);
        imageElement.attr("src", payload.imagePath);
    } else {
        console.error("No image data or path provided");
        return;
    }

    let delay = payload.delay || PROP_DEFAULT_MESSAGE;
    displayElement(ELEMENT_IMAGE);
    state.pageComplete(null, delay);
}

function finishImage(payload) {
    let delay = payload.delay || PROP_DEFAULT_IMAGE;
    state.pageComplete(null, delay);
}

function setImageSrc(imageSrc) {
    let imgObj = $("#" + ELEMENT_IMAGE);
    imgObj.attr("src", imageSrc);
    displayElement(ELEMENT_IMAGE);
}

function displayMessage(payload) {
    t4s_log("displayMessage");

    // Get title and right title
    let rightTitle = payload.rightTitle || "";
    let title = payload.title || state.getCompName();

    // Display titles
    displayTitle(title, rightTitle, payload.title ? STYLE_IMPORTANT : STYLE_EVENTNAME);

    // Get current layout
    const currentLayout = document.getElementById("layoutStylesheet").href.split('/').pop().replace('.css', '');

    // Ensure message styles are loaded
    if (!state.messageStyles) {
        state.loadMessageStyles();
    }

    // Get styles for current layout
    const layoutStyles = state.messageStyles[currentLayout] || {};

    // Display message content with styles
    $("#message1").html(payload.message1 || "");
    if (layoutStyles.message1 && layoutStyles.message1 !== "") {
        t4s_log("Applying message1 style: " + layoutStyles.message1);
        $("#message1").attr("style", layoutStyles.message1);
    }

    $("#message2").html(payload.message2 || "");
    if (layoutStyles.message2 && layoutStyles.message2 !== "") {
        t4s_log("Applying message2 style: " + layoutStyles.message2);
        $("#message2").attr("style", layoutStyles.message2);
    }

    $("#message3").html(payload.message3 || "");
    if (layoutStyles.message3 && layoutStyles.message3 !== "") {
        t4s_log("Applying message3 style: " + layoutStyles.message3);
        $("#message3").attr("style", layoutStyles.message3);
    }

    $("#message4").html(payload.message4 || "");
    if (layoutStyles.message4 && layoutStyles.message4 !== "") {
        t4s_log("Applying message4 style: " + layoutStyles.message4);
        $("#message4").attr("style", layoutStyles.message4);
    }

    let delay = payload.delay || PROP_DEFAULT_MESSAGE;
    displayElement(ELEMENT_MESSAGE);
    state.pageComplete(null, delay);
}

function displayMessageNumber(payload, number) {
    let messageObj = $("#message" + number);
    messageObj.html(payload["message" + number] || "");
    if (payload.style) {
        if (payload.style["message" + number] && payload.style["message" + number] !== "") {
            messageObj[0].style = payload.style["message" + number];
        }
    }
}

function displayStartList(payloadObj) {
    displayElement(ELEMENT_OFFICIAL_LIST);
    pageListDisplayObj = new PageListDisplay();
    pageListDisplayObj.displayStartListPage(payloadObj);
}

function isContentTypeField(type) {
    return type === TYPE_FIELD_STARTLIST || type === TYPE_FIELD_RESULTS;
}

function isContentTypeTrack(type) {
    return type === TYPE_TRACK_STARTLIST || type === TYPE_TRACK_RESULTS;
}

function isContentTypeStartList(type) {
    return type === TYPE_PF_STARTLIST || type === TYPE_TRACK_STARTLIST || type === TYPE_FIELD_STARTLIST;
}

function isContentTypeResultsList(type) {
    return type === TYPE_PF_RESULTS || type === TYPE_TRACK_RESULTS || type === TYPE_FIELD_RESULTS;
}

function displayOfficialResultsList(payloadObj) {
    pageListDisplayObj = new PageListDisplay();
    pageListDisplayObj.displayResultsListPage(payloadObj);
}

function getDisplayAthleteName(athleteName) {
    athleteName = athleteName.trim();
    athleteName = athleteName.split(" ");
    if ( athleteName.length > 1 ) {
        athleteName = athleteName[0].charAt(0).toUpperCase() + ". " + athleteName[1].replace(athleteName[0] + " ", "");
    }

    return athleteName;
}
function displaySingleResult(payloadObj) {
    displayElement(ELEMENT_RACE_SINGLERESULT);
    // displayEventName();
    displayCompName();
    $("#singleResultEvent").text(eventStore.getEventName());
    let result = payloadObj.RaceResults;
    // $("#resultEvent").text(getDefinedEventName(payloadObj.EventDetails));
    $("#singleResultBib").text(result.BibNumber);
    let athleteName = getDisplayAthleteName(result.AthleteName);
    $("#singleResultAthlete").html(athleteName);
    $("#singleResultAffiliation").text(result.Affiliation);
    if (result.Position !== "") {
        $("#singleResultPosLabel").text("Current Position : ");
    }else{
        $("#singleResultPosLabel").text("Attempted : ");
        result.Position = parseFloat(result.Performance).toFixed(2);
    }
    $("#singleResultPosValue").text(result.Position);
    if (payloadObj.EventDetails.height) {
        displaySingleHeight(payloadObj);
    } else {
        displaySingleDistance(payloadObj);
    }

    let delay = state.displayEvents.FRSingleDelay || PROP_DEFAULT_SINGLE_RESULT;

    state.pageComplete(null, delay);
}

function displaySingleHeight(payloadObj) {
    $(".singleResultDistanceRows").hide();
    $(".singleResultHeightRows").show();
    let bestAttempt = -1;
    let actualResults = [];
    let results = payloadObj.changedResult.results;
    let bestAttemptObj = null;
    for ( let r= 0;r < results.length; r++) {
        let result = results[r];
        let attempts = result.attempts;
        attempts = attempts.toUpperCase();
        if ( attempts !== "" && attempts !== "S" ){
            let actualAttempt = {
                result: result.height,
                attempts: attempts,
                record: result.record
            }
            actualResults.push(actualAttempt);
            if ( attempts.indexOf("O") > -1 ){
                bestAttempt = actualResults.length - 1;
            }
        }
    }
    if ( bestAttempt > -1 ) {
        $("#singleResultBestResultInfo").show();
        $("#singleResultBestResultLabel").text("Height Cleared : ");
        let bestObj = $("#singleResultBestResultValue");
        bestAttemptObj = actualResults[bestAttempt];
        bestAttemptObj.result = parseFloat(bestAttemptObj.result).toFixed(2)
        bestObj.text(bestAttemptObj.result);
        bestObj.addClass(SINGLEATTEMPTBESTCLASS);

        bestObj = $("#singleResultBestResultRecord");
        bestObj.text(bestAttemptObj.record);
    }else{
        $("#singleResultBestResultInfo").hide();
    }
    // clear results
    for (let r = 1; r <= SINGLEATTEMPTSDISPLAYED; r++) {
        let dispObj = $("#singleResultHeightT" + r);
        dispObj.html("");
    }
    let factor = 0;
    if ( actualResults.length > SINGLEATTEMPTSDISPLAYED ){
        factor = actualResults.length - SINGLEATTEMPTSDISPLAYED;
    }
    for(let r in actualResults) {
        // how many results to display
        let element = parseInt(r);
        if ( element >= factor ) {
            let actualResult = actualResults[r];
            let dispObj = $("#singleResultHeightT" + (element - factor + 1) );
            let html = "";
            let latestClass = "";
            if ( actualResults.length === (element + 1)) {
                latestClass = SINGLEATTEMPTLATESTCLASS;
            }
            if ( bestAttemptObj !== null ) {
                if (actualResult.result === bestAttemptObj.result) {
                    latestClass = SINGLEATTEMPTBESTCLASS;
                }
            }
            html += '<div class="singleResultHeightAttempt' + latestClass + '">' + actualResult.result + '</div>';
            html += '<div class="singleResultAttempts">' + getAttemptsHTML(actualResult.attempts) + '</div>';
            dispObj.html(html);
        }
    }
}

function getAttemptsHTML(attempts) {
    let attemptHTML = ""
    attempts += "   ";
    for (let a = 1; a <= 3; a++) {
        let attempt = attempts.charAt(a - 1);
        let useClass = "";
        attempt = attempt.toUpperCase();
        if ( attempt === "O" ){
            useClass = " " + SINGLEATTEMPTSUCCESSCLASS;
        }
        if ( attempt === "X" ){
            useClass = " " + SINGLEATTEMPTFAILCLASS;
        }
        attemptHTML += '<span class="singleResultAttempt';
        attemptHTML += useClass;
        attemptHTML += '">' + attempt + '</span>';
    }
    return attemptHTML;
}
function displaySingleDistance(payloadObj) {
    $(".singleResultDistanceRows").show();
    $(".singleResultHeightRows").hide();
    $("#singleResultBestResultAttempts").text("");

    let record = "";
    $("#singleResultScoreLabel").text("Distance : ");
    let bestPerf = payloadObj.changedResult.result || 0;
    if ( isNaN(bestPerf) ){
        bestPerf = 0;
    }else{
        bestPerf = parseFloat(bestPerf);
    }
    let latestTrial = 0;
    for (let t = 1; t <= 6; t++) {
        trialResultHTML = '<span class="singleResultTrialLabel singleResultDistanceLabel' + t + '"></span>';
        trialResultHTML += ('<span class="singleResultTrialResult"></span>');
        if (payloadObj.changedResult.results[t - 1]) {
            latestTrial = payloadObj.changedResult.results[t - 1].trial;
            let trialResult = payloadObj.changedResult.results[t - 1];
            if (trialResult.result !== "" && !isNaN(trialResult.result)) {
                trialResult.result = parseFloat(trialResult.result);
                if (trialResult.result > bestPerf) {
                    bestPerf = trialResult.result;
                    record = trialResult.record;
                }
            }
        }
    }
    $("#singleResultBestResultLabel").text("Best : ");
    let bestObj = $("#singleResultBestResultValue");
    if ( bestPerf > 0 ){
        bestPerf = bestPerf.toFixed(2);
    }else{
        bestPerf = "";
    }
    bestObj.text(bestPerf);
    bestObj.addClass(SINGLEATTEMPTBESTCLASS);
    $("#singleResultBestResultRecord").text(record);

    for (let t = 1; t <= 6; t++) {
        let trialResultHTML = "";
        let trialResult = "";
        let trial = t;
        if (payloadObj.changedResult.results[t - 1] && (payloadObj.changedResult.results[t - 1].result + payloadObj.changedResult.results[t - 1].status) !== "") {
            trial = payloadObj.changedResult.results[t - 1].trial;
            if (trial !== t) {
                let useT = t;
                while (useT < trial) {
                    trialResultHTML = '<span class="singleResultTrialLabel singleResultDistanceLabel' + useT + '">T' + useT + '</span>';
                    trialResultHTML += '<span class="singleResultTrialResult ' + SINGLEATTEMPTFAILCLASS + '">NM</span>';
                    let trialResultObj = $("#singleResultDistanceT" + useT);
                    trialResultObj.html(trialResultHTML);
                    useT++;
                }
            }

            trialResult = payloadObj.changedResult.results[t - 1].result;
            trialResultHTML = trialResult;
            if (trialResult === "" && payloadObj.changedResult.results[t - 1].status !== "ok") {
                trialResult = payloadObj.changedResult.results[t - 1].status;
            }

            if (trialResult !== "") {
                trialResult = ("" + trialResult).toUpperCase();
                let useClass = "";
                if (trialResult === 'X') {
                    useClass = " " + SINGLEATTEMPTFAILCLASS;
                }
                if (parseFloat(trialResult) === parseFloat(bestPerf) ) {
                    useClass = " " + SINGLEATTEMPTBESTCLASS;
                }
                if (!isNaN(trialResult)) {
                    trialResult = parseFloat(trialResult).toFixed(2);
                }
                trialResultHTML = '<span class="singleResultTrialLabel singleResultDistanceLabel' + trial + '">T' + trial + '</span>';
                trialResultHTML += ('<span class="singleResultTrialResult' + useClass + '">' + trialResult + '</span>');
            }
        }

        if (trialResultHTML !== "") {
            let trialResultObj = $("#singleResultDistanceT" + trial);
            trialResultObj.html(trialResultHTML);

            if (parseFloat(trialResult) === parseFloat(bestPerf) ) {
                trialResultObj.addClass(SINGLEATTEMPTBESTCLASS);
            } else {
                trialResultObj.removeClass(SINGLEATTEMPTBESTCLASS);
            }
        }
    }
}

function displayResultsImage(payloadObj) {
    setImageSrc(payloadObj.imagePath);
    finishImage(payloadObj);
}

function displayResultsList(payloadObj) {
    // if first result and not already shown, send first result to message and show
    let showResults = true;
    if (payloadObj.RaceResults.Results.length > 1) {
        let firstResult = payloadObj.RaceResults.Results[0];
        let secondResult = payloadObj.RaceResults.Results[1];
        if (firstResult.Performance !== "" && secondResult.Performance === "") {
            // first result, so show message instead
            showResults = false;
            payloadObj.transition = false;

            let useName = firstResult.AthleteName;
            if ( useName === firstResult.Affiliation) {
                useName = "";
            }else if ( useName.length > 17 ) {
                useName = getDisplayAthleteName(useName);
            }

            // Get message delay from race clock configuration, default to 3 seconds
            let messageDelay = (state.raceClockInfo.raceMessageDelay || 3) * MILLISECONDS_IN_SECOND; // Convert to milliseconds

            let msgPayload = {
                "ContentType":TYPE_MESSAGE,
                "resultMessage":true,
                "title": payloadObj.EventDetails.EventName,
                "rightTitle": payloadObj.RaceResults.WindReading,
                "message1":useName,
                "message2":firstResult.Affiliation,
                "message3":firstResult.Performance,
                "delay": messageDelay,
                "processed": false,
                "EventDetails": payloadObj.EventDetails // Include event details for proper tracking
            }
            // console.log("Injecting first result message into queue:", msgPayload);
            // console.log("Existing queue:", messageScheduler.messages);
            messageScheduler.injectMessage(msgPayload);

            // console.log("New queue:", messageScheduler.messages);

            // Send the first result message back to controller for message history
            msgPayload.controller = true;
            console.log("Sending first result message to controller:", msgPayload);
            sendBoardMessage(msgPayload);
        }
    }
    if (showResults) {
        displayElement(ELEMENT_RACE_RESULTS);
        if (pfResultsListPageObj === null) {
            pfResultsListPageObj = new PfResultsDisplay();
        }
        pfResultsListPageObj.displayResults(payloadObj);
    }
}

// results code
let pfResultsListPageObj = null;
let officialResultsListPageObj = null;

class PfResultsDisplay {
    DIV_TOP_ROWS = `raceResultsTopList`;
    DIV_OTHER_ROWS = 'raceResultsList';
    CLASS_TOP_ROWS_DIVIDER = 'raceResultsTopBorder';

    constructor() {
        t4s_log("pfResultsDisplay : Constructor");
        this.clearDisplay();
    }

    clearTimeouts() {
        console.log("Clear PF Timeouts");
    }

    clearDisplay() {
        $("#" + this.DIV_TOP_ROWS).empty();
        $("#" + this.DIV_OTHER_ROWS).empty();
        this.topRowsDivider(false);
        this.maxRows = 8;
        this.topRows = 3;
        this.results = [];
        this.lastPositionDisplayed = 0;
        this.rowsDisplayed = 0;
        this.rowsProcessed = 0;
        this.redisplay = false;
    }

    checkResults(messageInQueue, inboundMessage) {
        let reDisplay = false;
        let raceResultsInQueue = messageInQueue.RaceResults.Results;
        let raceResultsInbound = inboundMessage.RaceResults.Results;
        if (raceResultsInbound.length !== raceResultsInQueue.length) {
            reDisplay = true;
        } else {
            for (let i in raceResultsInQueue) {
                let raceResultInQueue = raceResultsInQueue[i];
                let raceResultInbound = raceResultsInbound[i];
                if (raceResultInQueue.Performance !== "" && raceResultInQueue.Performance !== raceResultInbound.Performance) {
                    reDisplay = true;
                    break;
                }
                if (raceResultInQueue.AthleteName !== raceResultInbound.AthleteName) {
                    reDisplay = true;
                    break;
                }
            }
        }
        this.redisplay = reDisplay;
    }
    // when putting result into array, check
    addResult(result) {
        t4s_log("pfResultsDisplay : addResult");
        if (this.results[result.Position] === undefined) {
            // add the result to this.results
            this.results[result.Position] = result;
            return;
        }
        if (this.results[result.Position].BibNumber === result.BibNumber) {
            return;
        }
        // results have changed, need to redisplay
        this.redisplay = true;
        this.results[result.Position] = result;
        // remove all results array elements after result.Position
        this.results = this.results.slice(0, result.Position + 1);
    }
    displayResults(payloadObj) {
        if (this.redisplay) {
            this.clearDisplay();
        }
        this.results = [];
        let results = payloadObj.RaceResults || [];
        let allResultsIn = true;
        let resultCnt = 1;
        for (const checkResult of results.Results) {
            checkResult.resultCnt = resultCnt++;
            if (checkResult.Performance !== "") {
                // result in
                this.results.push(checkResult);
            }else if(isNaN(checkResult.Position)) {
                // DQ or DNS
                this.results.push(checkResult);
            }else{
                allResultsIn = false;
            }
        }
        this.showResults(payloadObj);
        if (allResultsIn) {
            state.pageComplete(pfResultsListPageObj.displayFullResults);
        }
    }

    displayFullResults() {
        // reset message to be processed as a Track Result
        messageScheduler.messages[0].ContentType = TYPE_TRACK_RESULTS;
        messageScheduler.messages[0].processed = false;
    }

    topRowsDivider(show = true) {
        const resultsTopDiv = $("#" + this.DIV_TOP_ROWS);
        resultsTopDiv.toggleClass(this.CLASS_TOP_ROWS_DIVIDER, show);
    }

    showResults(payloadObj) {
        t4s_log("pfResultsDisplay : showResults");
        let rightTitle = " ";
        if (payloadObj.RaceResults.WindReading !== "") {
            rightTitle = "W:" + payloadObj.RaceResults.WindReading;
            rightTitle = rightTitle.replace("M/S", "");
        }
        displayEventName(rightTitle);
        let resultsDiv = $("#" + this.DIV_OTHER_ROWS);
        let resultsTopDiv = $("#" + this.DIV_TOP_ROWS);

        if (this.redisplay) {
            resultsDiv.empty();
            resultsTopDiv.empty();
            this.lastPositionDisplayed = 0;
        }

        for (let r in this.results) {
            let resultObj = this.results[r];

            // if (resultObj.Position <= this.lastPositionDisplayed) {
            if (resultObj.resultCnt <= this.lastPositionDisplayed) {
                continue;
            }
            // this.results starts at 1 so length will be one more than required
            if ((this.results.length - 1) > this.maxRows && this.lastPositionDisplayed >= this.topRows) {
                // no results currently shown and the past results are more than maxrows
                let lastRowsFirstPosition = this.results.length - (this.maxRows - this.topRows);
                // if (resultObj.Position < lastRowsFirstPosition) {
                if (resultObj.resultCnt < lastRowsFirstPosition) {
                    continue;
                }
            }
            // this.lastPositionDisplayed = resultObj.Position;
            this.lastPositionDisplayed = resultObj.resultCnt;

            if (this.lastPositionDisplayed > this.maxRows) {
                // add border to top rows
                this.topRowsDivider(true);
            }
            this.useResultElement = parseInt(r);
            if (this.rowsDisplayed >= this.maxRows) {
                // add border to top rows
                this.topRowsDivider(true);
                let obj = resultsDiv.find("div:first");
                obj.addClass("fade-out");
                setTimeout(function () {
                    $("#raceResultsList div").toggleClass("pfResultsListRowEven");
                    obj.remove();
                    pfResultsListPageObj.rowsDisplayed--;
                    pfResultsListPageObj.displayRow($("#" + pfResultsListPageObj.DIV_OTHER_ROWS), pfResultsListPageObj.results[pfResultsListPageObj.useResultElement], false);
                }, 500);
            } else {
                let toObj = resultsDiv;
                // if (resultObj.Position <= this.topRows) {
                if (resultObj.resultCnt <= this.topRows) {
                    toObj = resultsTopDiv;
                }
                this.displayRow(toObj, resultObj);
            }
        }
    }

    displayRow(toObj, resultObj, animate = true) {
        return new Promise((resolve) => {
            if (resultObj.AthleteName) {
                t4s_log("pfResultsDisplay : displayRow : " + resultObj.AthleteName);
            } else {
                t4s_log("pfResultsDisplay : displayRow : " + JSON.stringify(resultObj));
            }
            let position = "";
            this.rowsDisplayed++;
            if (animate) {
                position = "relative";
            }
            let useClass = "";
            if (pfResultsListPageObj.rowsDisplayed % 2 === 0) {
                useClass += "pfResultsListRowEven";
            }
            let resultPosition = resultObj.Position;
            let resultPerformance = resultObj.Performance;
            if ( isNaN(resultPosition)) {
                resultPerformance = resultPosition;
                resultPosition = "-";
            }
            let rowDiv = $("<div>")
                .addClass("pfResultsListRow")
                .addClass(useClass)
                .attr("resultposition", resultPosition);
            rowDiv.append($("<span>").text(resultPosition).addClass("pfResultsListPosition"));
            useClass = "pfResultsListAthlete";
            let athleteName = resultObj.AthleteName;
            let affiliation = resultObj.Affiliation;
            if ( athleteName === affiliation || athleteName === "") {
                athleteName = affiliation;
                affiliation = "";
                useClass = "pfResultsListTeam";
            } else {
                athleteName = getDisplayAthleteName(resultObj.AthleteName);
            }
            rowDiv.append($("<span>").text(athleteName).addClass(useClass).addClass("ellipse"));

            if ( affiliation !== "") {
                rowDiv.append($("<span>").text(affiliation).addClass("pfResultsListAffiliation").addClass("ellipse"));
            }
            rowDiv.append($("<span>").text(resultPerformance).addClass("pfResultsListPerf"));

            toObj.append(rowDiv);
            if (animate) {
                rowDiv.animate({opacity: 1, right: "0%"}, 300, resolve);
            } else {
                resolve();
            }
        });
    }
}

// Official List code
let pageListDisplayObj = null;

class PageListDisplay {
    constructor() {
        t4s_log("pageListDisplay : Constructor");
        this.pageEndTime = null;
        this.scrollInterval = 6000; // time in milliseconds to wait before restarting the display
        this.pageSize = 8; // number of entries per page
        this.currentPage = 0;
        this.callback = null;
        // random number;
        this.id = Math.floor(1000 + Math.random() * 9000);
    }

    clearTimeouts() {
        clearTimeout(this.pageEndTime);
    }

    displayStartListPage(payloadObj) {
        this.listRows = payloadObj.StartList ? payloadObj.StartList : payloadObj.RaceStartList ? payloadObj.RaceStartList.Starters : [];
        if (this.listRows.length === 0) {
            return;
        }
        this.eventDetails = getMessageId(payloadObj, false);
        t4s_log("displayStartListPage : " + this.eventDetails.EventNumber + "-" + this.eventDetails.RoundNumber);
        displayEventName("");

        this.type = payloadObj.ContentType;
        this.totalPages = Math.ceil(this.listRows.length / this.pageSize);
        this.currentPage = 0;
        this.buildListPage(this.type, CLASS_PREFIX_STARTLIST);
    }

    displayResultsListPage(payloadObj) {
        this.eventDetails = getMessageId(payloadObj, false);
        t4s_log("displayResultsListPage : " + this.eventDetails.EventNumber + "-" + this.eventDetails.RoundNumber);
        let rightTitle = "Results";
        if (payloadObj.RaceResults && payloadObj.RaceResults.WindReading) {
            if (payloadObj.RaceResults.WindReading !== "") {
                rightTitle = "W:" + payloadObj.RaceResults.WindReading;
            }
        }
        displayEventName(rightTitle);
        this.listRows = payloadObj.Results || (payloadObj.RaceResults.Results || []);
        this.type = payloadObj.ContentType;
        this.totalPages = Math.ceil(this.listRows.length / this.pageSize);
        this.currentPage = 0;
        this.buildListPage(this.type, CLASS_PREFIX_RESULTLIST);
    }

    buildListPage(listType, classPrefix) {
        t4s_log("buildListPage : " + this.eventDetails.EventNumber + " : " + this.eventDetails.RoundNumber);
        displayElement(ELEMENT_OFFICIAL_LIST);
        let listHeaderDiv = $("#officialListHeader");
        let listDiv = $("#officialListRows");
        listDiv.empty();
        listHeaderDiv.empty();

        let athleteClass = classPrefix + "FieldAthlete";
        if (isContentTypeTrack(listType)) {
            athleteClass = classPrefix + "TrackAthlete";
        }

        let start = this.currentPage * this.pageSize;
        let end = Math.min(start + this.pageSize, this.listRows.length);
        this.currentPage++;
        let animateRow = (i) => {
            if (i >= end) {
                // last row, check if another page or complete
                this.clearTimeouts();

                this.pageEndTime = setTimeout(() => {
                    if (this.currentPage >= this.totalPages) {
                        state.pageComplete(null, this.scrollInterval);
                    } else if ( pageListDisplayObj ){
                        pageListDisplayObj.buildListPage(listType, classPrefix);
                    }
                }, this.scrollInterval);
            } else {
                let rowObj = this.listRows[i];
                let bibNumber = ("" + rowObj.BibNumber).trim() || '-';
                let athleteName = rowObj.AthleteName.trim() || '-';
                let affiliation = rowObj.Affiliation.trim() || '-';
                if ( athleteName === affiliation || athleteName === "" || athleteName === "-") {
                    athleteName = affiliation;
                    affiliation = " ";
                }
                let useClass = "";
                if (i % 2 === 0) {
                    useClass += "resultsListRowEven";
                }
                let rowDiv = $("<div>")
                    .addClass(useClass)
                    .addClass(classPrefix + "Row");
                if (isContentTypeStartList(listType)) {
                    if (listType === TYPE_TRACK_STARTLIST || listType === TYPE_PF_STARTLIST) {
                        rowDiv.append($("<span>").text(rowObj.Lane).addClass(classPrefix + "Lane"));
                    }
                    rowDiv.append($("<span>").text(bibNumber).addClass(classPrefix + "Bib"));
                    let suffix = "Athlete";
                    if ( athleteName === affiliation) {
                        suffix = "Team";
                        affiliation = "";
                    }else{
                        athleteName = getDisplayAthleteName(athleteName);
                    }
                    rowDiv.append($("<span>").text(athleteName).addClass(classPrefix + suffix).addClass(athleteClass).addClass("ellipse"));
                    if ( affiliation !== "") {
                        rowDiv.append($("<span>").text(affiliation).addClass(classPrefix + "Affiliation").addClass("ellipse"));
                    }
                }
                if (isContentTypeResultsList(listType)) {
                    let position = rowObj.Position ?? '-';
                    let perfDisplay = rowObj.Performance;
                    if ( isNaN(position)){
                        position = "-";
                        perfDisplay = rowObj.Position;
                    }
                    rowDiv.append($("<span>").text(position).addClass(classPrefix + "Position"));

                    let suffix = "Athlete";
                    if ( athleteName === affiliation) {
                        suffix = "Team";
                        affiliation = "";
                    }else{
                        athleteName = getDisplayAthleteName(athleteName);
                    }
                    rowDiv.append($("<span>").text(athleteName).addClass(classPrefix + suffix).addClass("ellipse"));
                    if ( affiliation !== "") {
                        rowDiv.append($("<span>").text(affiliation).addClass(classPrefix + "Affiliation").addClass("ellipse"));
                    }

                    // if same hundreth, show thousandth
                    if (this.listRows[i + 1]) {
                        let nextPerf = this.listRows[i + 1].Performance;
                        if (rowObj.PerformanceDetail && nextPerf && nextPerf !== "" && nextPerf === perfDisplay) {
                            perfDisplay = rowObj.PerformanceDetail;
                        }
                    }
                    if (this.listRows[i - 1]) {
                        let prevPerf = this.listRows[i - 1].Performance;
                        if (rowObj.PerformanceDetail && prevPerf && prevPerf !== "" && prevPerf === perfDisplay) {
                            perfDisplay = rowObj.PerformanceDetail;
                        }
                    }
                    if ( !isNaN(perfDisplay)){
                        perfDisplay = parseFloat(perfDisplay).toFixed(2);
                    }else{
                        // let numbersPerfDisplay = perfDisplay.replaceAll(":","").replaceAll(".","");
                        // if ( isNaN(numbersPerfDisplay)){
                        //     perfDisplay = "";
                        // }
                    }
                    rowDiv.append($("<span>").text(perfDisplay).addClass(classPrefix + "Perf"));
                }
                listDiv.append(rowDiv);
                rowDiv.animate({opacity: 1}, 300, () => animateRow(i + 1));
            }
        };
        animateRow(start);
    }
}

function t4s_log(msg) {
    if (state.debug) {
        console.log(msg);
    }
}

// Debug function to log message styles
function logMessageStyles() {
    const currentLayout = document.getElementById("layoutStylesheet").href.split('/').pop().replace('.css', '');
    t4s_log("Current layout: " + currentLayout);
    t4s_log("All message styles: " + JSON.stringify(state.messageStyles));
    t4s_log("Current layout styles: " + JSON.stringify(state.messageStyles[currentLayout] || {}));
}
