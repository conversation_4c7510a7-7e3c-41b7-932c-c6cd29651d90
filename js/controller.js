
let raceStartTimes = [];
const STORE_PAYLOADS = 'payloads';
const STORE_RACE_START_TIMES = 'startTimes';
const STORE_TRANSITIONS = 'transitions';
const STORE_CURRENT_TAB = "activeControllerTab";
const MILLISECONDS_IN_SECOND = 1000;
const INFINITE_MESSAGE = -1;
const DROPDOWN_IMAGES = "";
const DROPDOWN_VIDEOS = "v";
const DROPDOWN_BT_VIDEOS = "bt";
const DROPDOWN_BT_SHOWS = "b";
const TYPE_BOARD_GROUPS = "boardGroups";

let state = {
    compInfo:null,
    activeBoardId:0,
    events: []
}
function processSocketMessageOnController(payloadObj) {
    if (typeof payloadObj === "string") {
        payloadObj = JSON.parse(payloadObj);
    }

    // Handle board info updates from other controllers
    if (payloadObj.ContentType === TYPE_BOARD_INFO) {
        // Save the board info to localStorage
        saveBoardInfo(payloadObj);

        // Refresh the active boards selector to show updated names
        activeBoards.populateSelector();

        console.log(`Board ${payloadObj.boardId} updated by another controller`);

        // If we're currently viewing this board, refresh the display
        if (activeBoards.getUIBoardId() === payloadObj.boardId) {
            // Update form fields with the new data
            updateControllerFormWithBoardInfo(payloadObj);
        }

        return;
    }

    // Handle message styles updates from other controllers
    if (payloadObj.ContentType === TYPE_MESSAGE_STYLES) {
        // Save message styles to localStorage
        if (payloadObj.messageStyles) {
            localStorage.setItem("messageStyles", JSON.stringify(payloadObj.messageStyles));
            console.log("Received message styles from another controller");

            // Update UI if we're on the message styles tab
            if ($("#messageStyleLayout").is(":visible")) {
                loadMessageStyles();
            }
        }
        return;
    }

    // Handle competition info updates from other controllers
    if (payloadObj.ContentType === TYPE_COMPINFO) {
        console.log("Received competition info:", payloadObj);
        if (payloadObj.config) {
            setCompInfo(payloadObj.config);
            updateUiConfig(payloadObj.config);
            console.log("Updated UI with config:", payloadObj.config);
        }
        return;
    }

    // Handle transitions updates from other controllers
    if (payloadObj.ContentType === TYPE_TRANSITIONS_UPDATE) {
        if (payloadObj.transitions) {
            console.log(`Received transitions update for board ${payloadObj.boardId}`);
            storeTransitions(payloadObj.transitions);
            // Update the transitions UI
            loadTransitions();
        }
        return;
    }

    // Handle board groups updates from other controllers
    if (payloadObj.ContentType === TYPE_BOARD_GROUPS) {
        if (payloadObj.boardGroups) {
            console.log("Received board groups update from another controller");
            boardGroups.groups = payloadObj.boardGroups;
            boardGroups.saveGroups();
            boardGroups.populateCompactGroups();
        }
        return;
    }

    // Handle active boards list updates from other controllers
    if (payloadObj.ContentType === "activeBoardsList") {
        if (payloadObj.boards) {
            console.log("Received active boards list update from another controller");
            activeBoards.boards = payloadObj.boards;
            activeBoards.saveBoards();
            activeBoards.populateSelector();
        }
        return;
    }

    switch (payloadObj.ContentType) {
        case TYPE_PF_STARTLIST:
        case TYPE_PF_RESULTS:
        case TYPE_MESSAGE:
        case TYPE_PF_STOPPED:
        case TYPE_IMAGE:
        case TYPE_VIDEO:
            if (payloadObj.ContentType === TYPE_MESSAGE) {
                console.log("Controller received message for history:", payloadObj);
            }
            checkAndAddBTVideo(payloadObj);
            payloadStore.addPayload(payloadObj);
            break;
        case TYPE_BOARD_ACTIVE:
            activeBoards.inboundActive(payloadObj);
            break;
        case TYPE_PF_START:
            const now = moment();
            handleRaceStartTime(now.format("HH:mm:ss.SS"));
            break;
        default:
            break;
    }
    payloadStore.displayPayloadStore();
}

function checkAndAddBTVideo(payload){
    let compInfo = getCompInfo();
    if ( !compInfo?.bluffPath ){
        return;
    }
    if ( payload.ContentType === TYPE_VIDEO && payload.url.includes(compInfo.bluffPath) ){
        const selectId = "btVideoSelect";
        const selectObj = $("#" + selectId);
        // get the file name from the URL
        const fileName = payload.url.split("/").pop();
        // check if the file is already in the select
        if ( !selectObj.find(`option[value='${fileName}']`).length ) {
            selectObj.append($("<option>").attr("value", fileName).text(fileName));
        }
        // sort the options for selectObj
        selectObj.html(selectObj.find("option").sort(function(a, b) {
            return a.text == b.text ? 0 : a.text < b.text ? -1 : 1;
        }));
    }
}
function autoResize(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = textarea.scrollHeight + 'px';
}

function getUIBoardsToSendTo() {
    // Check if "Send to All" is selected
    if ($("#sendToAll").is(":checked")) {
        return ALL_BOARDS; // 0 is the special value for "all boards"
    }

    // Otherwise return the selected boards
    return $("input[name='activeBoard']:checked").map(function () {
        return $(this).val();
    }).get();
}

function getUIBoardInfo() {
    // Get competition info to include branding style
    const compInfo = getCompInfo();

    return {
        "ContentType": TYPE_BOARD_INFO,
        "boardId": activeBoards.getUIBoardId(),
        "boardName": activeBoards.getUIBoardName(),
        "boardType": activeBoards.getUIBoardType(),
        "boardLayout": $("#boardLayout").val(),
        "events": getCompSelectedEvents(),
        "raceClockInfo": getRaceClockInfo(),
        "displayStartListTimes": getUIStartListTimes(),
        "transitions": getUITransitions(),
        "systemName": $("#systemName").val(),
        "brandingStyle": compInfo ? compInfo.brandingStyle : ""
    };
}

function getRaceClockInfo() {
    return {
        "raceLag": parseInt($("#raceLag").val()),
        "raceFormat": $("input[name='raceFormat']:checked").val(),
        "raceClockMessage": $("#raceClockMessage").is(":checked"),
        "raceMessageDelay": parseInt($("#firstMessageDelay").val()) || 3
    };
}

function bindEvents() {
    // Add event handlers for image selection
    $("#refreshImagesBtn").click(function() {
        console.log("Refresh images button clicked");
        loadFileNamesFromFolder().then((imageArray) => {
            populateDropdown(imageArray.files, DROPDOWN_IMAGES);
        });
    });

    $("#showSelectedImageBtn").click(function() {
        const selectedImage = $("#imageSelect").val();
        if (selectedImage) {
            showSelectedImage(selectedImage);
        } else {
            alert("Please select an image from the dropdown");
        }
    });
    $("#refreshVideosBtn").click(function() {
        console.log("Refresh Videos button clicked");
        loadFileNamesFromFolder(DROPDOWN_VIDEOS).then((videos) => {
            populateDropdown(videos.files, DROPDOWN_VIDEOS);
        });
    });
    $("#refreshBTVideosBtn").click(function() {
        console.log("Refresh BT Videos button clicked");
        loadFileNamesFromFolder(DROPDOWN_BT_VIDEOS).then((videos) => {
            populateDropdown(videos.files, DROPDOWN_BT_VIDEOS);
        });
    });

    $("#refreshBTMessageBtn").click(function() {
        console.log("Refresh BT Shows button clicked");
        loadFileNamesFromFolder(DROPDOWN_BT_SHOWS).then((videos) => {
            populateDropdown(videos.files, DROPDOWN_BT_SHOWS);
        });
    });

    // Initialize the main controller tabs
    $("#controller-tabs").tabs({
        activate: function(event, ui) {
            // Store the active tab index in localStorage when tab changes
            const activeTabIndex = $("#controller-tabs").tabs("option", "active");
            localStorage.setItem(STORE_CURRENT_TAB, activeTabIndex);
        }
    });

    // Restore the previously active tab after page load
    const savedTabIndex = localStorage.getItem(STORE_CURRENT_TAB);
    if (savedTabIndex !== null) {
        const tabIndex = parseInt(savedTabIndex);
        if (tabIndex >= 0 && tabIndex < $("#controller-tabs ul li").length) {
            $("#controller-tabs").tabs("option", "active", tabIndex);
        }
    }

    // Add event handlers for board type checkboxes
    $("input[name='showOption']").change(function() {
        displayRaceClock();
        displaySystemConfig();
        displayEventsSet();
    });


    // Initialize the style dialog
    $("#styleDialog").dialog({
        autoOpen: false,
        modal: true,
        width: 400,
        buttons: {
            "Apply": function() {
                const fontSize = $("#dialogFontSize").val();
                const fontColor = $("#dialogFontColor").val();
                const fontWeight = $("#dialogFontWeight").is(":checked") ? "bold" : "normal";
                const fontStyle = $("#dialogFontStyle").is(":checked") ? "italic" : "normal";

                // Build the CSS style string
                const styleString = `font-size: ${fontSize}; color: ${fontColor}; font-weight: ${fontWeight}; font-style: ${fontStyle};`;

                // Get the target field ID from the hidden input
                const targetField = $("#currentStyleField").val();

                // Set the style string to the appropriate hidden field
                $(`#${targetField}`).val(styleString);

                // Update the preview
                const previewId = targetField.replace("Style", "Preview");
                $(`#${previewId}`).attr("style", styleString);

                $(this).dialog("close");
            },
            "Cancel": function() {
                $(this).dialog("close");
            }
        }
    });

    // Message Styles tab handlers
    $("#messageStyleLayout").change(function() {
        // Load styles for the selected layout
        loadMessageStyles();
    });

    // Style buttons event handlers
    $("#styleMessageTitleBtn").click(function() {
        openStyleDialog("styleMessageTitleStyle");
    });

    $("#styleMessageRightTitleBtn").click(function() {
        openStyleDialog("styleMessageRightTitleStyle");
    });

    $("#styleMessage1Btn").click(function() {
        openStyleDialog("styleMessage1Style");
    });

    $("#styleMessage2Btn").click(function() {
        openStyleDialog("styleMessage2Style");
    });

    $("#styleMessage3Btn").click(function() {
        openStyleDialog("styleMessage3Style");
    });

    $("#styleMessage4Btn").click(function() {
        openStyleDialog("styleMessage4Style");
    });

    $("#saveMessageStylesBtn").click(function() {
        saveMessageStyles();
    });

    // Add event handler for the "Send to All" checkbox
    $("#sendToAll").change(function() {
        if ($(this).is(":checked")) {
            // Disable all individual board checkboxes
            $("input[name='activeBoard']").prop("disabled", true);
        } else {
            // Enable all individual board checkboxes
            $("input[name='activeBoard']").prop("disabled", false);
        }
    });

    // Clear selection button
    $("#clearSelectionBtn").click(function() {
        // Uncheck "Send to All" first
        $("#sendToAll").prop("checked", false);

        // Enable all individual board checkboxes (in case they were disabled by "Send to All")
        $("input[name='activeBoard']").prop("disabled", false);

        // Uncheck all individual board checkboxes
        $("input[name='activeBoard']").prop("checked", false);

        console.log("Selection cleared");
    });

    $("#uploadImageBtn").click(function () {
        let file = document.getElementById("fileUpload").files[0];
        let delay = parseInt($("#" + ELEMENT_IMAGE_DELAY).val());
        let reader = new FileReader();
        reader.onload = function (e) {
            let message = {
                "ContentType": TYPE_IMAGE,
                "fileName": file.name,
                "delay": delay === INFINITE_MESSAGE ? INFINITE_MESSAGE : delay * MILLISECONDS_IN_SECOND,
                "image": e.target.result
            };
            sendControllerMessage(message);
        };
        reader.readAsDataURL(file);
    });

    $("#showVideoBtn").click(function () {
        let delay = parseInt($("#" + ELEMENT_VIDEO_DELAY).val());
        let url = $("#videoUrl").val();
        let message = {
            "ContentType": TYPE_VIDEO,
            "url": url,
            "delay": delay === INFINITE_MESSAGE ? INFINITE_MESSAGE : delay * MILLISECONDS_IN_SECOND
        };
        // payloadStore.addPayload(message);
        sendControllerMessage(message);
    });
    $("#showSelectedVideoBtn").click(function () {
        let delay = parseInt($("#" + ELEMENT_VIDEO_DELAY).val());
        let url = state.compInfo.videoPath + "/" + $("#videoSelect").val();
        let message = {
            "ContentType": TYPE_VIDEO,
            "url": url,
            "delay": delay === INFINITE_MESSAGE ? INFINITE_MESSAGE : delay * MILLISECONDS_IN_SECOND
        };
        // payloadStore.addPayload(message);
        sendControllerMessage(message);
    });
    $("#showSelectedBTVideoBtn").click(function () {
        let delay = parseInt($("#" + ELEMENT_VIDEO_DELAY).val());
        let url = state.compInfo.bluffPath + "/videos/" + $("#btVideoSelect").val();
        let message = {
            "ContentType": TYPE_VIDEO,
            "url": url,
            "delay": delay === INFINITE_MESSAGE ? INFINITE_MESSAGE : delay * MILLISECONDS_IN_SECOND
        };
        // payloadStore.addPayload(message);
        sendControllerMessage(message);
    });
    $("#showClockBtn").click(function () {
        let message = getBaseMessage(TYPE_LIVE_CLOCK);
        sendControllerMessage(message);
    });
    $("#showRaceClockBtn").click(function () {
        let message = getBaseMessage(TYPE_RACE_CLOCK);
        sendControllerMessage(message);
    });
    $("#reloadBtn").click(function () {
        let message = getBaseMessage(TYPE_RACE_RELOAD);
        sendControllerMessage(message);
    });
    $("#finishMessageBtn").click(function () {
        let message = getBaseMessage(TYPE_CANCEL_MESSAGE);
        sendControllerMessage(message);
    });
    $("#loadCompBtn").click(function () {
        getCompInfo();
    });
    $("#saveCompBtn").click(function () {
        let options = {
            "compName": $("#compName").val(),
            "compId": $("#compId").text(),
            "eventNameFormat": $('#eventNameFormat').val(),
            "photoFolder": $('#photoFolder').val().replaceAll("\\","/"),
            "imagePath": $('#imagePath').val().replaceAll("\\","/"),
            "videoPath": $('#videoPath').val().replaceAll("\\","/"),
            "bluffDrive": $('#bluffDrive').val(),
            "bluffPath": $('#bluffPath').val().replaceAll("\\","/"),
            "brandingStyle": $('#brandingStyle').val()
        };
        saveCompInfo(options);
        initActiveBoards();
    });

    $("#saveBoardBtn").click(function () {
        // Get the current board info
        let boardInfo = getUIBoardInfo();

        // Save it locally
        saveBoardInfo(boardInfo);

        // Add a flag to indicate this should be broadcast to all controllers
        boardInfo.broadcastUpdate = true;

        // Send it to all controllers
        sendControllerMessage(boardInfo);

        // Update the document title
        document.title = activeBoards.getUIBoardId() + " : " + activeBoards.getUIBoardName();

        console.log("Board saved and broadcast to all controllers");
    });
    $("#resetBtn").click(function () {
        let message = {
            "ContentType": "Race Reset"
        };
        sendControllerMessage(message);
    });

    $("#messageBtn").click(function () {
        let delay = parseInt($("#" + ELEMENT_MESSAGE_DELAY).val());
        let message = {
            "ContentType": "message",
            "title": $("#messageTitle").val(),
            "rightTitle": $("#messageRightTitle").val(),
            "message1": $("#message1").val(),
            "message2": $("#message2").val(),
            "message3": $("#message3").val(),
            "message4": $("#message4").val(),
            "btShow": $("#btMessageSelect").val(),
            "socket": true,
            "delay": delay === INFINITE_MESSAGE ? INFINITE_MESSAGE : delay * MILLISECONDS_IN_SECOND
        };
        sendControllerMessage(message);
    });

    $("#clearMessgeBtn").click(function () {
        $("#messageTitle").val("");
        $("#messageRightTitle").val("");
        $("#message1").val("");
        $("#message2").val("");
        $("#message3").val("");
        $("#message4").val("");
        $("#" + ELEMENT_MESSAGE_DELAY).val(PROP_DEFAULT_MESSAGE / MILLISECONDS_IN_SECOND);
    });

    $("#sendStartTimeBtn").on("click", function() {
        const selectedTime = $("#raceStartTimeSelect").val();
        if (selectedTime) {
            const message = {
                "ContentType": TYPE_PF_SAVE_START,
                "startTime": selectedTime,
                "boardId": ALL_BOARDS
            };
            sendControllerMessage(message);
        } else {
            alert("Please select a start time");
        }
    });

    $("#addCustomTimeBtn").on("click", function() {
        const customTime = $("#manualStartTime").val();
        if (customTime && /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9](\.[0-9]{1,2})?$/.test(customTime)) {
            // Add to dropdown if valid time format
            if (!raceStartTimes.includes(customTime)) {
                raceStartTimes.unshift(customTime);
                if (raceStartTimes.length > 5) {
                    raceStartTimes.pop();
                }
                saveRaceStartTimes(raceStartTimes);
                updateRaceStartTimeDropdown();
                $("#raceStartTimeSelect").val(customTime);
            }
        } else {
            alert("Please enter a valid time in format HH:MM:SS.SS");
        }
    });

    $("#styleTitleBtn").click(function() {
        openStyleDialog("messageStyleTitle");
    });

    $("#styleRightTitleBtn").click(function() {
        openStyleDialog("messageStyleRightTitle");
    });

    $("#styleBtn1").click(function() {
        openStyleDialog("messageStyle1");
    });

    $("#styleBtn2").click(function() {
        openStyleDialog("messageStyle2");
    });

    $("#styleBtn3").click(function() {
        openStyleDialog("messageStyle3");
    });
    // If the search parm compid is present, set the value of the compNumber input field
    $("#saveTransitionsBtn").click(function() {
        saveTransitions();
        $("#saveBoardBtn").click();
    });
    // When board layout changes, update message style layout to match
    $("#boardLayout").change(function() {
        $("#messageStyleLayout").val($(this).val());
        loadMessageStyles();
    });

    // Board group event handlers
    $("#createGroupBtn").click(function() {
        const selectedBoards = [];
        $("input[name='activeBoard']:checked").each(function() {
            selectedBoards.push($(this).val());
        });
        if (selectedBoards.length < 2) {
            alert("Please select at least two boards for the group");
            return;
        }
        const name = prompt("Enter group name:");
        if (name) {
            boardGroups.addGroup(name, selectedBoards);
        }
    });

    // Add event handler for btMessageSelect change
    $("#btMessageSelect").change(function() {
        const selectedOption = $(this).find("option:selected");
        for(let i = 1; i < 5; i++){
            let messageText = selectedOption.data("message" + i);

            if (!messageText) {
                messageText = "Message " + i;
            }
            $("[for=message" + i + "]").text(messageText);
        }
    });
}

function controllerReady(compConfig) {
    setCompInfo(compConfig);
    updateUiConfig(compConfig);

    // Load board groups before binding events
    boardGroups.loadGroups();
    boardGroups.populateCompactGroups();

    bindEvents();

    activeBoards.populateSelector();
    raceStartTimes = loadRaceStartTimes();
    updateRaceStartTimeDropdown();

    payloadStore.displayPayloadStore()
    $("#tabs").tabs();

    // Load board settings (this will also call the display functions)
    activeBoards.loadActiveBoardSettings();

    // Initialize all transition fields visibility
    const transitionOptions = getAllTransitionOptions();

    transitionOptions.forEach(option => {
        toggleTransitionFields(option);
    });
    loadTransitions();

    // When the page loads, set the message style layout to match the board layout
    $("#messageStyleLayout").val($("#boardLayout").val());
    loadMessageStyles();

    // Load images and videos when the controller is ready
    loadImagesAndVideos();
}

function updateUiConfig(compInfo) {
    if (compInfo) {
        loadEvents(compInfo.compId);
        $("#compId").text(compInfo.compId || "");
        $("#compName").val(compInfo.compName || "");
        $("#eventNameFormat").val(compInfo.eventNameFormat || "");
        $("#photoFolder").val(compInfo.photoFolder || "");
        $("#imagePath").val(compInfo.imagePath || "");
        $("#videoPath").val(compInfo.videoPath || "");
        $("#bluffDrive").val(compInfo.bluffDrive || "D");
        $("#bluffPath").val(compInfo.bluffPath || "");
        $("#brandingStyle").val(compInfo.brandingStyle || "");

        if ( compInfo.bluffPath && compInfo.bluffPath.length > 0) {
            $("#btMessageSelectDiv").show();
        }else{
            $("#btMessageSelectDiv").hide();
        }
        console.log("Loaded compInfo from server:", compInfo);
    } else {
        console.log("No compInfo found");
    }
}

function loadEvents(compId) {
    let options = {};
    if (compId > 0) {
        options.compId = compId;
    }
    $.get("/getevents", options, function (data) {
        // already sorted by type, then starttime and heatno, so should be in correct order
        for (const element of data) {
            let event = element;
            if (state.events[event.type] === undefined) {
                state.events[event.type] = [];
            }
            state.events[event.type].push(event);
        }
        displayEventsSet();
    });
}

function displayRaceClock() {
    if ($("#showRaceTime").is(":checked")) {
        $("#displayRaceClock").show();
    } else {
        $("#displayRaceClock").hide();
    }
}

function displayEventsSet(type = "") {
    let boardTypes = activeBoards.getUIBoardType();
    $("#eventSet").hide();
    $("#singleDelayDiv").hide();

    let active = 0;
    displayEventSet("TS", boardTypes, 0);
    displayEventSet("TR", boardTypes, 1);
    if (type === "TR") {
        active = 1;
    }
    displayEventSet("FS", boardTypes, 2);
    if (type === "FS") {
        active = 2;
    }
    displayEventSet("FR", boardTypes, 3);
    if (type === "FR" || type === "FRSingle") {
        active = 3;
    }
    displayEventSet("FI", boardTypes, 4);
    if (type === "FI") {
        active = 4;
    }
    displayEventSet("TI", boardTypes, 5);
    if (type === "TI") {
        active = 5;
    }
    if (boardTypes.includes("FRSingle")) {
        $("#singleDelayDiv").show();
    } else {
        $("#singleDelayDiv").hide();
    }
    if (boardTypes.includes("TSList") || boardTypes.includes("FSList")) {
        $("#displayStartListTimes").show();
    } else {
        $("#displayStartListTimes").hide();
    }
    $("#tabs").tabs("option", "active", active);
}

function displayEventSet(type, boardTypes, tabNo) {
    if (boardTypes.includes(type + "List") || boardTypes.includes(type + "Single") || type[1] === "I") {
        $("#eventSet").show();
        $("#tab-" + type).show();
        $("#tabs").tabs("option", "active", tabNo);
        populateEvents(type);
    } else {
        $("#tab-" + type).hide();
    }
}

function populateEvents(type) {
    let events = getSelectedEvents(type);
    let spanId = type + "List";
    if (events.length === 0) {
        $("#" + spanId).text("All Events Selected");
        $("#" + spanId + "-Events").html("");
    } else {
        $("#" + spanId).text(events.length + " Events Selected");
        $("#" + spanId + "-Events").html("<table class=\"eventListTable\" id='" + spanId + "-Table'><tr><th>Event</th><th>Heat</th><th>Start Time</th><th>Gender</th><th>Age Group</th><th>Stage</th><th>Event Id</th></tr></table>");
        if (state.events[type[0]]) {
            events.sort();
            events.forEach(eventInfo => {
                let eventId = eventInfo.split("-")[0];
                let heatNo = eventInfo.split("-")[1];
                let event = state.events[type[0]].find(e => ("" + e.eventid) === eventId && e.heatNo === parseInt(heatNo));
                if (event) {
                    let startDt = new Date(event.starttime).toLocaleString('en-GB', {
                        day: '2-digit',
                        month: '2-digit',
                        year: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                    $("#" + spanId + "-Table").append("<tr><td><span title='" + event.name + "'>" + event.title + "</span></td><td>" + event.heatNo + "</td><td>" + startDt + "</td><td>" + event.gender + "</td><td>" + event.ageGroup + "</td><td>" + event.stage + "</td><td>" + event.eventid + "</td></tr>");
                }
            });
        }
    }
}

function saveSelectedEvents(type, values) {
    const boardId = activeBoards.getUIBoardId();
    let boardInfo = getBoardInfo(boardId);

    // Initialize events object if it doesn't exist
    if (!boardInfo.events) {
        boardInfo.events = {};
    }

    // Save the events for this type
    boardInfo.events[type] = values;

    // Save the updated board info
    saveBoardInfo(boardInfo);
}

function getSelectedEvents(type) {
    const boardId = activeBoards.getUIBoardId();
    const boardInfo = getBoardInfo(boardId);

    // If the board has events and this type exists, return them
    if (boardInfo.events && boardInfo.events[type]) {
        return boardInfo.events[type];
    }

    // Otherwise return an empty array
    return [];
}

function getUIStartListTimes() {
    let times = [];
    for (let i = 1; i <= 3; i++) {
        let from = parseInt($("#slFrom_" + i).val());
        let to = parseInt($("#slTo_" + i).val());
        times.push({from: from, to: to});
    }
    return times;
}

function getCompSelectedEvents() {
    return {
        "TS": getSelectedEvents("TS"),
        "TR": getSelectedEvents("TR"),
        "FS": getSelectedEvents("FS"),
        "FR": getSelectedEvents("FR"),
        "FI": getSelectedEvents("FI"),
        "TI": getSelectedEvents("TI"),
        "FRSingleDelay": parseInt($("#singleDelay").val())
    };
}

function selectEvents(type) {
    if (state.events[type[0]] === undefined) {
        alert("Warning: No events available for the selected type!");
        return;
    }
    let currentSelected = getSelectedEvents(type);
    let events = state.events[type[0]];

    // sort events using the element starttime which is in format 2025-07-27T15:20:00.000Z
    events.sort((a, b) => {
        return a.starttime.localeCompare(b.starttime);
    });

    // First, check if dialog exists and destroy it
    if ($("#eventDialog").hasClass("ui-dialog-content")) {
        $("#eventDialog").dialog("destroy");
    }

    // Remove any existing dialog from the DOM
    $("#eventDialog").remove();

    // Create a new dialog element
    $("body").append('<div id="eventDialog"></div>');
    let dialog = $("#eventDialog");

    // Create the dialog content
    dialog.append("<div>Select events to display on this board</div>");
    let table = $("<table>").addClass("eventTable");
    dialog.append(table);

    // Add table headers
    let headerRow = $("<tr>");
    table.append(headerRow);
    headerRow.append($("<th>").text("All Heats"));
    headerRow.append($("<th>").text("This Heat"));
    headerRow.append($("<th>").text("Start Time").css("width", "160px"));
    headerRow.append($("<th>").text("Event Name"));
    headerRow.append($("<th>").text("Gender"));
    headerRow.append($("<th>").text("Age Group"));
    headerRow.append($("<th>").text("Stage"));
    headerRow.append($("<th>").text("Event ID"));

    let processedEvents = [];

    // Add your existing code for populating the table rows here
    for (const element of events) {
        let selected = currentSelected.includes(element.eventid + "-" + element.heatNo);
        let event = element;
        if ( event.name === "" ){
            continue;
        }
        if (processedEvents.includes(event.eventid)) {
            continue;
        }
        processedEvents.push(event.eventid);
        let row = $("<tr>");
        table.append(row);
        let cell = $("<td>");
        row.append(cell);
        let input = "&nbsp;";
        if (event.heatNo < 2) {
            input = $("<input>").attr("type", "checkbox").attr("name", "eventSelect").attr("value", event.eventid).attr("checked", selected).attr("eventid", event.eventid).attr("eventmaster", true);
        }
        cell.append(input);
        cell = $("<td>");
        row.append(cell);
        input = $("<input>").attr("type", "checkbox").attr("name", "eventSelect").attr("value", event.eventid + "-" + event.heatNo).attr("checked", selected).attr("eventid", event.eventid).attr("heatno", event.heatNo);
        cell.append(input);
        let startDt = new Date(event.starttime);
        startDt = startDt.toLocaleString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
        row.append($("<td>").text(startDt));
        row.append($("<td class='ellipse'>").text(event.name));
        row.append($("<td>").text(event.gender));
        row.append($("<td>").text(event.ageGroup));
        row.append($("<td>").text(event.stage));
        row.append($("<td>").text(event.eventid));
    }

    dialog.dialog({
        modal: true,
        title: "Select Events",
        width: 1000,
        height: 800,
        open: function () {
            // bind click event to all checkboxes
            $("input[eventmaster='true']").click(function () {
                let checked = $(this).prop("checked");
                let eventid = $(this).attr("eventid");
                $("input[eventid=" + eventid + "]").prop("checked", checked);
            });
        },
        buttons: {
            "Deselect All": function () {
                $("input[name='eventSelect']").prop("checked", false);
            },
            "OK": function () {
                let selectedEvents = [];
                $("input[name='eventSelect']:checked:not([eventmaster])").each(function () {
                    selectedEvents.push($(this).val());
                });
                console.log("Selected Events : " + selectedEvents);
                saveSelectedEvents(type, selectedEvents);
                displayEventsSet(type);
                $(this).dialog("close");
            },
            "Cancel": function () {
                $(this).dialog("close");
            }
        }
    });
}

let activeBoards = {
    boards: [],
    key: "activeBoards",
    getUIBoardName: function() {
        return $("#boardName").val();
    },

    getUIBoardId: function() {
        let boardId = $("#boardId").val();
        if (boardId === "") {
            boardId = 0;
        } else {
            boardId = parseInt(boardId);
        }
        return boardId;
    },
    getUIBoardType: function() {
        return $("input[name='showOption']:checked").map(function () {
            return $(this).val();
        }).get();
    },
    saveBoards() {
        localStorage.setItem(this.key, JSON.stringify(this.boards));
    },
    populateSelector() {
        // clear the select
        let selectObj = $("#activeBoardsSelect");
        selectObj.html("");
        selectObj.append($("<option>").attr("value", "").text("Select Board"));
        // empty the activeBoards selector
        $("#activeBoardSelections").html("");

        const boards = this.getBoards();
        const totalBoards = boards.length;

        if (totalBoards === 0) {
            $("#activeBoardSelections").append('<div style="padding: 10px; color: #666;">No boards added</div>');
            return;
        }

        // Always use 3 columns for better layout
        const columnsCount = 3;

        // Create column containers with fixed 3-column grid
        const columnsContainer = $("<div>").css({
            "display": "grid",
            "grid-template-columns": "1fr 1fr 1fr",
            "gap": "10px",
            "padding": "5px"
        });

        // Create columns
        const columns = [];
        for (let i = 0; i < columnsCount; i++) {
            const column = $("<div>");
            columns.push(column);
            columnsContainer.append(column);
        }

        boards.forEach((boardId, index) => {
            let boardObj = getBoardInfo(boardId);
            let boardName = boardObj.boardName;
            if (!boardName) {
                boardName = "Unknown";
            }

            // Distribute boards evenly across columns
            const columnIndex = index % columnsCount;

            // add as a checkbox item to the appropriate column
            columns[columnIndex].append(
                $("<div>").addClass("checkbox-container").append(
                    $("<input>")
                        .attr("type", "checkbox")
                        .attr("name", "activeBoard")
                        .attr("value", boardId)
                        .attr("id", "activeBoard_" + boardId)
                ).append(
                    $("<label>")
                        .attr("for", "activeBoard_" + boardId)
                        .text(boardId + " : " + boardName)
                ).append(
                    $("<button>")
                        .addClass("remove-board-btn")
                        .attr("onclick", `activeBoards.removeBoardFromList(${boardId})`)
                        .text("Remove")
                )
            );

            selectObj.append($("<option>").attr("value", boardId).text(boardId + " : " + boardName));
        });

        $("#activeBoardSelections").append(columnsContainer);
    },
    getBoards() {
        let boards = localStorage.getItem(this.key);
        if (boards) {
            this.boards = JSON.parse(boards);
        }
        return this.boards;
    },
    inboundActive(msg) {
        let boardId = msg.boardId;
        let board = this.boards.find(activeBoardId => activeBoardId === boardId);
        if (!board) {
            this.boards.push(boardId);
            this.saveBoards();
            this.populateSelector();
        }
    },
    addBoardToList() {
        // ask for a board id then add this to the active board select
        let boardId = prompt("Enter Board ID");
        if (boardId) {
            boardId = parseInt(boardId);
            // Check if board already exists in the list
            if (this.boards.includes(boardId)) {
                alert(`Board ID ${boardId} already exists in the list.`);
                return;
            }
            this.boards.push(boardId);
            this.saveBoards();
            this.populateSelector();

            // Broadcast the updated board list to all controllers
            this.broadcastBoardList();
        }
    },
    removeBoardFromList(boardId) {
        // Get board info for a more descriptive confirmation message
        let boardObj = getBoardInfo(boardId);
        let boardName = boardObj.boardName || "Unknown";

        // Ask for confirmation before removing
        if (confirm(`Are you sure you want to remove board ${boardId}: ${boardName} from the list?`)) {
            // Remove the board ID from the list
            this.boards = this.boards.filter(id => id !== boardId);
            this.saveBoards();
            this.populateSelector();
        }
    },
    uiNameChanged() {
        let boardId = activeBoards.getUIBoardId();
        let boardName = activeBoards.getUIBoardName();
        let board = this.boards.find(activeBoardId => activeBoardId === boardId);
        if (board) {
            let boardObj = getBoardInfo(boardId);
            boardObj.boardName = boardName;
            saveBoardInfo(boardObj);
            this.saveBoards();
            this.populateSelector();
        }
    },
    uiIdSelected() {
        // the selected board has changed, so update state and display
        activeBoards.saveActiveBoardId();

        // Load the board settings without reloading the page
        activeBoards.loadActiveBoardSettings();

        // Update the send-to-boards checkboxes to reflect the current board selection
        activeBoards.populateSelector();
    },
    getActiveBoardId: function() {
        const boardId = localStorage.getItem("activeBoardId");
        return boardId ? parseInt(boardId) : 0;
    },
    saveActiveBoardId: function() {
        const boardId = parseInt($("#activeBoardsSelect").val());
        localStorage.setItem("activeBoardId", boardId);
    },
    loadActiveBoardSettings: function(){
        const boardId = activeBoards.getActiveBoardId();
        if (boardId === 0) {
            return;
        }
        const boardObj = getBoardInfo(boardId);
        if (boardObj) {
            // Use the complete board info loading function
            updateControllerFormWithBoardInfo(boardObj);
        }
    },
    broadcastBoardList() {
        const message = {
            "ContentType": "activeBoardsList",
            "boards": this.boards,
            "broadcastUpdate": true,
            "boardId": [ALL_BOARDS] // Explicitly set to send to all boards
        };
        sendControllerMessage(message);
        console.log("Board list broadcast to all controllers");
    }
}

function initActiveBoards() {
    activeBoards.boards = [];
    activeBoards.saveBoards();
}

class PayloadStore {
    constructor(limit = 30) {
        this.limit = limit;
        this.payloads = this.loadPayloads();
    }

    addPayload(payload) {
        if ( payload.ContentType === TYPE_IMAGE){
            return;
        }
        eventStore.storeEvent(payload);
        let existingIndex = -1;

        for (let i = 0; i < this.payloads.length; i++) {
            let p = this.payloads[i];
            let key = "";
            if (p.ContentType === payload.ContentType) {
                if (p.ContentType === TYPE_MESSAGE) {
                    key = "title";
                } else if (p.ContentType === TYPE_VIDEO) {
                    key = 'url';
                } else if (p.EventDetails.EventNumber === payload.EventDetails.EventNumber) {
                    if (p.EventDetails.HeatNumber === payload.EventDetails.HeatNumber) {
                        if (p.EventDetails.RoundNumber === payload.EventDetails.RoundNumber) {
                            existingIndex = i;
                            break;
                        }
                    }
                }
            }
            if (key !== "" && p[key] === payload[key]) {
                existingIndex = i;
                break;
            }
        }

        if (existingIndex > -1) {
            this.payloads[existingIndex] = payload;
        } else {
            this.payloads.unshift(payload);
        }
        // if limit reached, remove the oldest payload
        if (this.payloads.length > this.limit) {
            this.payloads.pop();
        }
        // save payloads to local storage
        this.savePayloads();
    }

    loadPayloads() {
        const storedPayloads = localStorage.getItem(STORE_PAYLOADS);
        return storedPayloads ? JSON.parse(storedPayloads) : [];
    }

    savePayloads() {
        localStorage.setItem(STORE_PAYLOADS, JSON.stringify(this.payloads));
    }

    getPayloads() {
        return this.payloads;
    }

    displayPayloadStore() {
        const payloads = payloadStore.getPayloads();
        let displayText = '';
        for (let index in payloads) {
            let payload = payloads[index];
            if (payload.delay === undefined) {
                payload.delay = 15;
            }
            if (payload.delay > MILLISECONDS_IN_SECOND) {
                payload.delay = payload.delay / MILLISECONDS_IN_SECOND;
            }
            if (payload.delay < 0) {
                payload.delay = INFINITE_MESSAGE;
            }
            if (payload.ContentType === TYPE_MESSAGE) {
                displayText += `<div class="ctrl-messageRow">
                    <div class="ctrl-eventType">${payload.ContentType}</div>
                    <div class="ctrl-eventNumber">${payload.delay}</div>
                    <div class="ctrl-eventName">${payload.title}</div>
                    <div class="ctrl-btnDiv">
                        <button class="ctrl-btn" onclick="payloadStore.sendPayload(${index});">Send</button>
                    </div>
                </div>`;
            } else if (payload.ContentType === TYPE_PF_RESULTS) {
                displayText += `<div class="ctrl-messageRow">
                    <div class="ctrl-eventType">${payload.ContentType}</div>
                    <div class="ctrl-eventNumber">${payload.EventDetails.EventNumber}</div>
                    <div class="ctrl-eventName">${eventStore.getEventNameByEventDetails(payload.EventDetails)}</div>
                    <div class="ctrl-btnDiv">
                        <button class="ctrl-btn" onclick="payloadStore.sendPayload(${index});">Send</button>
                    </div>
                    <div class="ctrl-btnDiv">
                        <button class="ctrl-btn" onclick="payloadStore.toggleResults(${index});">Results</button>
                    </div>
                </div>`;
                displayText += `<div id="results-${index}" style="display:none;">`;
                for (let i = 0; i < payload.RaceResults.Results.length; i++) {
                    let result = payload.RaceResults.Results[i];
                    displayText += `<div class="ctrl-messageRow">
                        <div class="ctrl-eventType">${result.Position}</div>
                        <div class="ctrl-eventNumber">${result.Performance}</div>
                        <div class="ctrl-eventName">${result.AthleteName}</div>
                        <div class="ctrl-btnDiv">
                            <button class="ctrl-btn" onclick="payloadStore.copyToMessage(${index},${i});">Put In Message</button>
                        </div>
                    </div>`;
                }
                displayText += `</div>`;
            } else if (payload.ContentType === TYPE_IMAGE) {
                displayText += `<div class="ctrl-messageRow">
                    <div class="ctrl-eventType">${payload.ContentType}</div>
                    <div class="ctrl-eventNumber">${payload.delay}</div>
                    <div class="ctrl-eventName">${payload.fileName}</div>
                    <div class="ctrl-btnDiv">
                        <button class="ctrl-btn" onclick="payloadStore.sendPayload(${index});">Send</button>
                    </div>
                </div>`;
            } else if (payload.ContentType === TYPE_VIDEO) {
                displayText += `<div class="ctrl-messageRow">
                    <div class="ctrl-eventType">${payload.ContentType}</div>
                    <div class="ctrl-eventNumber">${payload.delay}</div>
                    <div class="ctrl-eventName">${payload.url}</div>
                    <div class="ctrl-btnDiv">
                        <button class="ctrl-btn" onclick="payloadStore.sendPayload(${index});">Send</button>
                    </div>
                </div>`;
            } else {
                displayText += `<div class="ctrl-messageRow">
                    <div class="ctrl-eventType">${payload.ContentType}</div>
                    <div class="ctrl-eventNumber">${payload.EventDetails.EventNumber}</div>
                    <div class="ctrl-eventName">${eventStore.getEventNameByEventDetails(payload.EventDetails)}</div>
                    <div class="ctrl-btnDiv">
                        <button class="ctrl-btn" onclick="payloadStore.sendPayload(${index});">Send</button>
                    </div>
                </div>`;
            }
        }

        $('#messageList').html(displayText);
    }

    copyToMessage(index, resultIndex) {
        const payload = this.payloads[index];
        const result = payload.RaceResults.Results[resultIndex];
        let title = payload.RaceResults.EventName;
        title = getDefinedEventName(payload.EventDetails,"",true);
        $("#messageTitle").val("");
        $("#message1").val(title);
        $("#message2").val(result.AthleteName);
        $("#message3").val(result.Performance);
        $("#message4").val("");
        $("#messageDelay").val(15);

        // Switch to the Actions tab after populating message fields
        $("#controller-tabs").tabs("option", "active", 4);
    }

    toggleResults(index) {
        const resultsDiv = document.getElementById(`results-${index}`);
        if (resultsDiv.style.display === "none") {
            resultsDiv.style.display = "block";
        } else {
            resultsDiv.style.display = "none";
        }
    }

    sendPayload(index) {
        const payload = this.payloads[index];
        sendControllerMessage(payload);
    }
}

const payloadStore = new PayloadStore();

// New function to update the controller form with board info
function updateControllerFormWithBoardInfo(boardInfo) {
    // Set board ID
    $("#boardId").val(boardInfo.boardId || "");

    // Set board name
    $("#boardName").val(boardInfo.boardName || "");

    // Set board layout if available
    if (boardInfo.boardLayout) {
        $("#boardLayout").val(boardInfo.boardLayout);
    }

    // Set system name if available
    if (boardInfo.systemName) {
        $("#systemName").val(boardInfo.systemName);
    }

    // Clear all board type checkboxes
    $("input[name='showOption']").prop("checked", false);

    // Set board type checkboxes
    if (boardInfo.boardType && Array.isArray(boardInfo.boardType)) {
        boardInfo.boardType.forEach(type => {
            $(`input[name="showOption"][value="${type}"]`).prop("checked", true);
        });
    }

    // Update race clock info if available
    if (boardInfo.raceClockInfo) {
        $("#raceLag").val(boardInfo.raceClockInfo.raceLag || 1);
        $(`input[name="raceFormat"][value="${boardInfo.raceClockInfo.raceFormat || 'Tenths'}"]`).prop("checked", true);
        $("#raceClockMessage").prop("checked", boardInfo.raceClockInfo.raceClockMessage || false);
        $("#messageDelay").val(boardInfo.raceClockInfo.messageDelay || 3);
    }

    // Update start list times if available
    if (boardInfo.displayStartListTimes && boardInfo.displayStartListTimes.length >= 3) {
        for (let i = 0; i < 3; i++) {
            if (boardInfo.displayStartListTimes[i]) {
                $("#slFrom_" + (i+1)).val(boardInfo.displayStartListTimes[i].from || 0);
                $("#slTo_" + (i+1)).val(boardInfo.displayStartListTimes[i].to || 0);
            }
        }
    }

    // Update single result delay if available
    if (boardInfo.events && boardInfo.events.FRSingleDelay !== undefined) {
        $("#singleDelay").val(boardInfo.events.FRSingleDelay);
    }

    // Update transitions if available
    if (boardInfo.transitions) {
        // Clear all checkboxes first
        $("input[name='transitionOption']").prop("checked", false);

        // For each transition, check the checkbox and set values
        Object.keys(boardInfo.transitions).forEach(option => {
            const transition = boardInfo.transitions[option];
            if (transition.enabled) {
                $(`#transition${option}`).prop("checked", true);

                if (transition.file) {
                    $(`#transitionFile_${option}`).val(transition.file);
                }

                if (transition.duration) {
                    $(`#transitionDuration_${option}`).val(transition.duration);
                }

                // Make sure the fields are displayed
                toggleTransitionFields(option);
            }
        });

        // Save the transitions to local storage
        localStorage.setItem(STORE_TRANSITIONS, JSON.stringify(boardInfo.transitions));
    }

    // Update display sections based on board type
    displayRaceClock();
    displaySystemConfig();
    displayEventsSet();

    // Update document title
    document.title = activeBoards.getUIBoardId() + " : " + activeBoards.getUIBoardName();
}

function handleRaceStartTime(startTime) {
    if (!raceStartTimes.includes(startTime)) {
        // Add the new start time to the beginning of the array
        raceStartTimes.unshift(startTime);

        // Keep only the last 5 start times
        if (raceStartTimes.length > 5) {
            raceStartTimes.pop();
        }
        saveRaceStartTimes(raceStartTimes);
        updateRaceStartTimeDropdown();
    }
}

function saveRaceStartTimes(startTimes){
    localStorage.setItem(STORE_RACE_START_TIMES, JSON.stringify(startTimes));
}
function loadRaceStartTimes(){
    const storedStartTimes = localStorage.getItem(STORE_RACE_START_TIMES);
    return storedStartTimes ? JSON.parse(storedStartTimes) : [];
}
function updateRaceStartTimeDropdown() {
    const select = $("#raceStartTimeSelect");
    select.html("<option value=''>Select a start time</option>");

    raceStartTimes.forEach(time => {
        select.append($("<option>").attr("value", time).text(time));
    });
}

function getSelectedBoardIds() {
    if ($("#sendToAllBoards").is(":checked")) {
        return ALL_BOARDS;
    }

    const selectedBoards = [];
    $("input[name='activeBoard']:checked").each(function() {
        selectedBoards.push($(this).val());
    });
    return selectedBoards;
}

// Add hidden style fields to store the style strings
$("<input>").attr({
    type: "hidden",
    id: "messageStyle1",
    name: "messageStyle1"
}).appendTo("#messages");

$("<input>").attr({
    type: "hidden",
    id: "messageStyle2",
    name: "messageStyle2"
}).appendTo("#messages");

$("<input>").attr({
    type: "hidden",
    id: "messageStyle3",
    name: "messageStyle3"
}).appendTo("#messages");

// Function to open the style dialog and parse existing style
function openStyleDialog(targetFieldId) {
    // Set the current target field
    $("#currentStyleField").val(targetFieldId);

    // Get existing style string
    const styleString = $(`#${targetFieldId}`).val();

    // Reset dialog to defaults
    $("#dialogFontSize").val("5em");
    $("#dialogFontColor").val("#ffffff");
    $("#dialogFontWeight").prop("checked", false);
    $("#dialogFontStyle").prop("checked", false);

    // Parse existing style if it exists
    if (styleString) {
        // Extract font size
        const fontSizeMatch = styleString.match(/font-size:\s*([^;]+)/);
        if (fontSizeMatch && fontSizeMatch[1]) {
            $("#dialogFontSize").val(fontSizeMatch[1].trim());
        }

        // Extract color
        const colorMatch = styleString.match(/color:\s*([^;]+)/);
        if (colorMatch && colorMatch[1]) {
            $("#dialogFontColor").val(colorMatch[1].trim());
        }

        // Extract font weight
        const boldMatch = styleString.match(/font-weight:\s*([^;]+)/);
        if (boldMatch && boldMatch[1].trim() === "bold") {
            $("#dialogFontWeight").prop("checked", true);
        }

        // Extract font style
        const italicMatch = styleString.match(/font-style:\s*([^;]+)/);
        if (italicMatch && italicMatch[1].trim() === "italic") {
            $("#dialogFontStyle").prop("checked", true);
        }
    }

    // Open the dialog
    $("#styleDialog").dialog("open");
}

// Function to update all style previews
function updateStylePreviews() {
    // Update each preview with its corresponding style
    for (let i = 1; i <= 3; i++) {
        const styleString = $(`#messageStyle${i}`).val();
        if (styleString) {
            $(`#stylePreview${i}`).attr("style", styleString);
        }
    }
    const styleTitleString = $(`#messageStyleTitle`).val();
    $(`#stylePreviewTitle`).attr("style", styleTitleString);
    const styleRightTitleString = $(`#messageStyleRightTitle`).val();
    $(`#stylePreviewRightTitle`).attr("style", styleRightTitleString);

}

function toggleTransitionFields(optionValue) {
    // replace all spaces in optionValue
    optionValue = optionValue.replace(/\s+/g, '');
    const checkbox = document.getElementById('transition' + optionValue);
    const fields = document.getElementById('transitionFields_' + optionValue);

    if (checkbox && fields) {
        fields.style.display = checkbox.checked ? 'block' : 'none';
    }
}

// New function to collect transition settings
function getUITransitions() {
    const transitions = {};
    const transitionOptions = getAllTransitionOptions();

    transitionOptions.forEach(option => {
        option = option.replace(/\s+/g, '');
        const checkbox = document.getElementById('transition' + option);
        if (checkbox && checkbox.checked) {
            transitions[option] = {
                enabled: true
            };

            // Add file and duration for options that have these fields
            const fileInput = document.getElementById('transitionFile_' + option);
            const durationInput = document.getElementById('transitionDuration_' + option);

            if (fileInput && durationInput) {
                // Store just the filename, not the full path for security reasons
                const filename = fileInput.value ? fileInput.value.split('\\').pop() : '';
                transitions[option].file = filename;
                transitions[option].duration = parseInt(durationInput.value) || 10;
            }
        }
    });

    return transitions;
}
function getAllTransitionOptions(){
    const transitionOptions = [
        TYPE_LIVE_CLOCK,
        TYPE_PF_RESET,
        TYPE_PF_IMAGE,
        TYPE_PF_STARTLIST,
        TYPE_PF_RESULTS,
        TYPE_TRACK_STARTLIST,
        TYPE_TRACK_RESULTS,
        TYPE_FIELD_STARTLIST,
        TYPE_FIELD_RESULTS,
        TYPE_FIELD_RESULT,
        TYPE_MESSAGE,
        TYPE_IMAGE,
        TYPE_VIDEO
    ];
    return transitionOptions;
}

// Add these functions to save and load transitions
function saveTransitions() {
    const transitions = getUITransitions();
    storeTransitions(transitions);

    // Create a message to broadcast transitions to all controllers
    const message = {
        "ContentType": TYPE_TRANSITIONS_UPDATE,
        "transitions": transitions,
        "boardId": activeBoards.getUIBoardId(),
        "broadcastUpdate": true
    };

    // Send to all controllers
    sendControllerMessage(message);
}
function storeTransitions(transitions){
    localStorage.setItem(STORE_TRANSITIONS, JSON.stringify(transitions));
}
function loadTransitions() {
    const storedTransitions = localStorage.getItem(STORE_TRANSITIONS);
    if (storedTransitions) {
        const transitions = JSON.parse(storedTransitions);

        // Clear all checkboxes first
        $("input[name='transitionOption']").prop("checked", false);

        // For each stored transition, check the checkbox and set values
        Object.keys(transitions).forEach(option => {
            const transition = transitions[option];
            if (transition.enabled) {
                $(`#transition${option}`).prop("checked", true);

                if (transition.file) {
                    $(`#transitionFile_${option}`).val(transition.file);
                }

                if (transition.duration) {
                    $(`#transitionDuration_${option}`).val(transition.duration);
                }

                // Make sure the fields are displayed
                toggleTransitionFields(option);
            }
        });
    }
}

function displaySystemConfig() {
    const boardTypes = activeBoards.getUIBoardType();
    const showSystemConfig = boardTypes.includes(BOARD_TYPE_RACECLOCK) ||
        boardTypes.includes(BOARD_TYPE_PFSLISTS) ||
        boardTypes.includes(BOARD_TYPE_PFRLISTS) ||
        boardTypes.includes(BOARD_TYPE_PFIMAGE);

    $("#displaySystemConfig").toggle(showSystemConfig);
}

// Function to save message styles to localStorage
function saveMessageStyles() {
    const currentLayout = $("#messageStyleLayout").val();

    // Get all saved styles first
    const allStyles = getAllSavedMessageStyles();

    // Update styles for current layout
    allStyles[currentLayout] = {
        title: $("#styleMessageTitleStyle").val(),
        rightTitle: $("#styleMessageRightTitleStyle").val(),
        message1: $("#styleMessage1Style").val(),
        message2: $("#styleMessage2Style").val(),
        message3: $("#styleMessage3Style").val(),
        message4: $("#styleMessage4Style").val()
    };

    // Save all styles back to localStorage
    localStorage.setItem("messageStyles", JSON.stringify(allStyles));

    // Create message to send to all boards
    const message = {
        "ContentType": TYPE_MESSAGE_STYLES,
        "messageStyles": allStyles,
        "boardId": ALL_BOARDS
    };

    sendControllerMessage(message);
}

// Function to get all saved message styles
function getAllSavedMessageStyles() {
    const savedStyles = localStorage.getItem("messageStyles");
    return savedStyles ? JSON.parse(savedStyles) : {};
}

// Function to load saved message styles
function loadMessageStyles() {
    const currentLayout = $("#messageStyleLayout").val();

    // Get styles for current layout
    const allStyles = getAllSavedMessageStyles();
    const layoutStyles = allStyles[currentLayout] || {};

    // Set styles
    $("#styleMessageTitleStyle").val(layoutStyles.title || "");
    $("#styleMessageRightTitleStyle").val(layoutStyles.rightTitle || "");
    $("#styleMessage1Style").val(layoutStyles.message1 || "");
    $("#styleMessage2Style").val(layoutStyles.message2 || "");
    $("#styleMessage3Style").val(layoutStyles.message3 || "");
    $("#styleMessage4Style").val(layoutStyles.message4 || "");

    // Update previews
    updateMessageStylePreviews();
}

// Function to update message style previews
function updateMessageStylePreviews() {
    $("#styleMessageTitlePreview").attr("style", $("#styleMessageTitleStyle").val());
    $("#styleMessageRightTitlePreview").attr("style", $("#styleMessageRightTitleStyle").val());
    $("#styleMessage1Preview").attr("style", $("#styleMessage1Style").val());
    $("#styleMessage2Preview").attr("style", $("#styleMessage2Style").val());
    $("#styleMessage3Preview").attr("style", $("#styleMessage3Style").val());
    $("#styleMessage4Preview").attr("style", $("#styleMessage4Style").val());
}

// Add this to the existing event handlers
$("#messageStyleLayout").change(function() {
    // Load styles for the selected layout
    loadMessageStyles();
});

function saveBoardInfo(boardInfo) {
    // Make sure we have a valid board ID
    if (!boardInfo.boardId) {
        console.error("Cannot save board info: missing boardId");
        return;
    }

    // Store the board info in localStorage
    localStorage.setItem('boardInfo_' + boardInfo.boardId, JSON.stringify(boardInfo));

    console.log(`Saved board info for board ${boardInfo.boardId}`);
}

function getCompInfo() {
    return state.compInfo;
}
function setCompInfo(compInfo) {
    state.compInfo = compInfo;
}
function saveCompInfo(compInfo) {
    if (!compInfo) {
        compInfo = {
            compId: -1,
            compName: "Not Known",
            eventNameFormat: "{{EVENTSTART}} {{EVENTNAME}} {{EVENTTITLE}} {{EVENTAGE}} {{EVENTGENDER}} {{EVENTSTAGE}}",
            photoFolder: "",
            imagePath: "",
            videoPath: "",
            bluffDrive: "",
            bluffPath: "",
            brandingStyle: ""
        };
    }
    state.compInfo = compInfo;
// write ajax to the updateConfig end point
    $.ajax({
        url: "/updateconfig",
        type: "POST",
        data: JSON.stringify(compInfo),
        contentType: "application/json",
        success: function(response) {
            console.log("Competition config updated:", response);
        },
        error: function(xhr, status, error) {
            console.error("Error updating competition config:", error);
        }
    });
}
let imageSelectObj = null;
function loadImagesAndVideos() {
    const proms = [ ()=> loadFileNamesFromFolder(DROPDOWN_IMAGES), ()=>loadFileNamesFromFolder(DROPDOWN_VIDEOS), ()=>loadFileNamesFromFolder(DROPDOWN_BT_SHOWS), ()=>loadFileNamesFromFolder(DROPDOWN_BT_VIDEOS)];
    executePromisesSequentiallyWithResults(proms).then(
        (results) => {
            // results will be an array of images then videos
            if ( results[0] ) {
                populateDropdown(results[0].files, DROPDOWN_IMAGES);
            }
            if (results[1]){
                populateDropdown(results[1].files, DROPDOWN_VIDEOS);
            }
            if (results[2]) {
                populateDropdown(results[2].files, DROPDOWN_BT_SHOWS);
            }
            if (results[3]) {
                populateDropdown(results[3].files, DROPDOWN_BT_VIDEOS);
            }
        }
    );
}
function populateDropdown(files, fileType = "") {
    const video = fileType === DROPDOWN_VIDEOS;
    const btShow = fileType === DROPDOWN_BT_SHOWS;
    const btVideo = fileType === DROPDOWN_BT_VIDEOS;
    const image = fileType === DROPDOWN_IMAGES;

    const selectId = video ? "videoSelect" : btVideo ? "btVideoSelect" : (btShow ? "btMessageSelect" : "imageSelect");
    const selectObj = $("#" + selectId);
    selectObj.html('<option value="">Select a' + (video ? " video" : btVideo ? " BT Video" : btShow ? " BluffTitler Show" : "n image") + '</option>');
    for (let sub in files) {
        const file = files[sub];
        if ( fileType === DROPDOWN_BT_SHOWS ){
            let params = "";
            if ( file.descriptions && file.descriptions.length > 0 ){
                for(let sub in file.descriptions){
                    let param = file.descriptions[sub];
                    params += `data-${param.description}="${param.name}" `;
                }
            }
            selectObj.append(`<option ${params} value="${file.filename}">${file.filename}</option>`);
        } else {
            selectObj.append(`<option value="${file}">${file}</option>`);
        }
    };
}
/**
 * Executes an array of promise-returning functions sequentially and returns their results
 * Uses plain JavaScript promises without async/await or TypeScript
 * @param promiseFunctions Array of functions that return promises
 * @returns Promise that resolves to an array of resolved values from each promise
 */
function executePromisesSequentiallyWithResults(promiseFunctions) {
    const results = [];

    return promiseFunctions.reduce((chain, fn) => {
        return chain.then(() => {
            return fn().then(result => {
                results.push(result);
                return results;
            });
        });
    }, Promise.resolve());
}

async function loadFileNamesFromFolder(type = "") {
    const compInfo = getCompInfo();
    const filePath = (type === DROPDOWN_VIDEOS ? compInfo?.videoPath : type === DROPDOWN_BT_VIDEOS ? compInfo?.bluffPath + "/videos" : type === DROPDOWN_BT_SHOWS ? compInfo?.bluffPath + "/shows" : compInfo?.imagePath) || "";

    if (!filePath) {
        return;
    }

    console.log("Loading files from folder:", filePath);
    // Request the list of images from the server
    return $.ajax({
        url: "/getFileTypes?type=" + type,
        type: "GET",
        data: { folder: filePath },
        success: function(response) {
            console.log("File Types loaded:", response);
            if (response.files && response.files.length > 0) {
                return response.files;
            }
        },
        error: function(xhr, status, error) {
            console.error("Error loading images:", error);
            console.error("Response:", xhr.responseText);
            alert("Error loading images: " + (xhr.responseJSON?.error || error));
        }
    });
}

function showSelectedImage(imageName) {
    const compInfo = getCompInfo();
    const imagePath = compInfo?.imagePath || "";
    const delay = parseInt($("#" + ELEMENT_IMAGE_DELAY).val());

    console.log("Showing selected image:", imageName, "from path:", imagePath);

    if (!imagePath) {
        alert("Please specify an image folder in the Competition Information tab");
        return;
    }

    let message = {
        "ContentType": TYPE_IMAGE,
        "fileName": imageName,
        "delay": delay * MILLISECONDS_IN_SECOND,
        "imagePath": `/${imagePath}/${imageName}`
    };

    sendControllerMessage(message);
}

let boardGroups = {
    groups: [],
    key: "boardGroups",

    saveGroups() {
        localStorage.setItem(this.key, JSON.stringify(this.groups));
    },

    loadGroups() {
        const saved = localStorage.getItem(this.key);
        if (saved) {
            this.groups = JSON.parse(saved);
        }
        return this.groups;
    },

    addGroup(name, boardIds) {
        const group = {
            id: Date.now(),
            name: name,
            boardIds: boardIds || []
        };
        this.groups.push(group);
        this.saveGroups();
        this.populateCompactGroups();
        this.broadcastGroups();
        console.log("Added group:", group);
        return group;
    },

    updateGroup(groupId, name, boardIds) {
        const group = this.groups.find(g => g.id === groupId);
        if (group) {
            group.name = name;
            group.boardIds = boardIds;
            this.saveGroups();
            this.populateCompactGroups();
            this.broadcastGroups();
        }
    },

    deleteGroup(groupId) {
        if (confirm("Are you sure you want to delete this group?")) {
            this.groups = this.groups.filter(g => g.id !== groupId);
            this.saveGroups();
            this.populateCompactGroups();
            this.broadcastGroups();
        }
    },

    selectGroup(groupId) {
        const group = this.groups.find(g => g.id === groupId);
        if (group) {
            // Uncheck "Send to All" first
            $("#sendToAll").prop("checked", false).trigger("change");
            // Uncheck all boards first
            $("input[name='activeBoard']").prop("checked", false);
            // Check boards in this group
            group.boardIds.forEach(boardId => {
                $(`input[name='activeBoard'][value='${boardId}']`).prop("checked", true);
            });
            console.log(`Selected group: ${group.name} with boards: ${group.boardIds.join(', ')}`);
        }
    },

    populateCompactGroups() {
        const container = $("#boardGroupsCompact");
        container.empty();

        if (this.groups.length === 0) {
            container.append('<span style="color: #666; font-style: italic; font-size: 12px;">No groups</span>');
            return;
        }

        this.groups.forEach(group => {
            const boardCount = group.boardIds.length;
            const groupBtn = $("<button>")
                .addClass("group-compact-btn")
                .css({
                    "padding": "4px 8px",
                    "margin": "0",
                    "font-size": "12px",
                    "background-color": "#e9ecef",
                    "color": "#495057",
                    "border": "1px solid #ced4da",
                    "border-radius": "3px",
                    "cursor": "pointer"
                })
                .text(`${group.name} (${boardCount})`)
                .attr("title", `Select boards: ${group.boardIds.join(', ')}`)
                .click(() => this.selectGroup(group.id));

            const deleteBtn = $("<button>")
                .css({
                    "padding": "2px 6px",
                    "margin-left": "2px",
                    "font-size": "10px",
                    "background-color": "#dc3545",
                    "color": "white",
                    "border": "none",
                    "border-radius": "2px",
                    "cursor": "pointer"
                })
                .text("×")
                .attr("title", "Delete group")
                .click((e) => {
                    e.stopPropagation();
                    this.deleteGroup(group.id);
                });

            const groupContainer = $("<div>")
                .css({
                    "display": "inline-flex",
                    "align-items": "center",
                    "background-color": "#f8f9fa",
                    "border-radius": "4px",
                    "padding": "2px"
                })
                .append(groupBtn)
                .append(deleteBtn);

            container.append(groupContainer);
        });
    },

    broadcastGroups() {
        const message = {
            "ContentType": TYPE_BOARD_GROUPS,
            "boardGroups": this.groups,
            "broadcastUpdate": true
        };
        sendControllerMessage(message);
    }
};
