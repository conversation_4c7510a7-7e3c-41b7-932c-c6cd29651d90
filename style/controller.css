/* Style button */
.styleBtn {
    background-color: #6c757d;
    color: white;
    min-width: 60px;
    margin-left: 10px;
}

.styleBtn:hover {
    background-color: #5a6268;
}

/* Style preview */
.style-preview {
    display: inline-block;
    margin-left: 10px;
    padding: 3px 8px;
    border: 1px dashed #ccc;
    border-radius: 3px;
    min-width: 80px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.05);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 150px;
}

/* Style dialog */
.style-dialog-row {
    margin: 15px 0;
    display: flex;
    align-items: center;
}

.style-dialog-row label {
    width: 100px;
    display: inline-block;
}

.style-dialog-row select,
.style-dialog-row input[type="color"] {
    width: 150px;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 3px;
}

/* Make the message input wider since we removed the style input */
.messageInput {
    width: 60%;
    padding: 8px;
    margin: 4px 0;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-size: 1em;
    height: 36px;
}
.messageTitle {
    width: 40%;
}
body {
    font-family: Arial, sans-serif;
    background-color: white !important;
}
.compName {
    width: 50%
}
.ctrl-Messages {
    width: 100%;
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden; /* Prevent horizontal scrolling */
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}
.ctrl-MessageRow {
    display: flex;
    flex-wrap: wrap; /* Allow wrapping on small screens */
    padding: 10px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
    width: 100%;
    box-sizing: border-box; /* Include padding in width calculation */
}
.ctrl-MessageRow:hover {
    background-color: #f0f0f0;
}
.ctrl-MessageRow:last-child {
    border-bottom: none;
}
.ctrl-eventType {
    flex: 0 0 30%; /* Slightly reduced to prevent overflow */
    min-width: 150px;
    padding-right: 10px;
    font-weight: bold;
    color: #444;
    word-break: break-word; /* Allow long words to break */
}
.ctrl-eventNumber {
    flex: 0 0 10%;
    min-width: 50px;
    padding-right: 10px;
    color: #666;
}
.ctrl-eventName {
    flex: 1; /* Take remaining space */
    min-width: 100px;
    padding-right: 10px;
    color: #333;
    word-break: break-word; /* Allow long words to break */
    overflow: hidden;
    text-overflow: ellipsis;
}
.ctrl-btnDiv {
    flex: 0 0 auto; /* Only take as much space as needed */
    text-align: right;
    margin-left: auto; /* Push to the right */
}
.ctrl-btn {
    padding: 5px 10px;
    background-color: #4a89dc;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
}
.ctrl-btn:hover {
    background-color: #3a79cc;
}
.eventListTable td{
    padding-right: 80px;
}
.eventListTable th{
    text-align: left;
}
.raceLag {
    width: 50px;
}
#activeBoardSelections {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 10px;
    max-height: 200px;
    overflow-y: auto;
}

.checkbox-container {
    display: grid;
    grid-template-columns: 25px 1fr 90px;
    align-items: center;
    padding: 4px 6px;
    position: relative;
    font-size: 12px;
    margin-bottom: 1px;
    border-bottom: 1px solid #dee2e6;
}

.checkbox-container:nth-child(odd) {
    background-color: #f8f9fa;
}

.checkbox-container:nth-child(even) {
    background-color: #ffffff;
}

.checkbox-container input[type="checkbox"] {
    margin: 0;
    justify-self: center;
}

.checkbox-container label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-left: 5px;
    font-size: 11px;
    cursor: pointer;
}

.remove-board-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 2px;
    padding: 2px 4px;
    font-size: 9px;
    margin: 0;
    min-width: 45px;
    min-height: 18px;
    cursor: pointer;
}

.remove-board-btn:hover {
    background-color: #c82333;
}

/* Clear selection button styling */
#clearSelectionBtn {
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

#clearSelectionBtn:hover {
    background-color: #5a6268;
}

/* Group button hover effects */
.group-compact-btn:hover {
    background-color: #d1ecf1 !important;
    border-color: #bee5eb !important;
}

#boardGroupsCompact button:hover + button {
    background-color: #c82333 !important;
}

/* Simple tab styling that won't break existing functionality */
#controller-tabs .ui-tabs-nav {
    background: #f5f5f5;
    border-radius: 4px 4px 0 0;
    padding: 5px 5px 0 5px;
    border-bottom: 1px solid #ddd;
}

#controller-tabs .ui-tabs-nav li {
    margin: 0 2px;
}

#controller-tabs .ui-tabs-nav li a {
    color: #777; /* Non-active tabs have grey text */
    padding: 8px 15px;
    text-decoration: none;
}

#controller-tabs .ui-tabs-nav li.ui-tabs-active a {
    color: #333; /* Active tab has darker text */
    font-weight: bold;
}

#controller-tabs .ui-tabs-panel {
    padding: 15px 5px;
}

/* Ensure the tabs don't break the layout */
#controller-tabs {
    margin-bottom: 20px;
}

/* Clear floats after tabs */
#controller-tabs:after {
    content: "";
    display: table;
    clear: both;
}

/* Results section styling */
[id^="results-"] {
    margin-left: 10px;
    margin-right: 10px;
    padding: 10px;
    background-color: #f5f5f5;
    border-left: 3px solid #4a89dc;
    margin-bottom: 10px;
    box-sizing: border-box;
    width: calc(100% - 20px); /* Account for margins */
    overflow-x: hidden;
}

/* Alternating row colors for better readability */
.ctrl-MessageRow:nth-child(even) {
    background-color: #f5f5f5;
}

.ctrl-MessageRow:nth-child(odd) {
    background-color: #ffffff;
}

/* Message timestamp styling */
.message-timestamp {
    font-size: 11px;
    color: #999;
    margin-bottom: 3px;
}

/* Message content preview */
.message-preview {
    white-space: normal; /* Allow wrapping */
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    font-style: italic;
    color: #777;
    font-size: 12px;
    margin-top: 3px;
}

/* Responsive adjustments for small screens */
@media (max-width: 768px) {
    .ctrl-MessageRow {
        flex-direction: column;
    }
    
    .ctrl-eventType, 
    .ctrl-eventNumber, 
    .ctrl-eventName {
        flex: 0 0 100%;
        margin-bottom: 5px;
    }
    
    .ctrl-btnDiv {
        width: 100%;
        text-align: left;
        margin-top: 5px;
    }
}

/* Send to All styling */
.send-to-all {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f0f7ff;
    border: 1px solid #d0e3ff;
    border-radius: 4px;
}

.send-to-all label {
    color: #0056b3;
}

/* Disabled checkboxes styling */
input[name='activeBoard']:disabled + label {
    color: #999;
    font-style: italic;
}

/* Standardized button styling */
button, 
input[type="button"], 
input[type="submit"] {
    /* Font */
    font-family: Arial, sans-serif;
    font-size: 13px;
    font-weight: normal;
    
    /* Size and spacing */
    padding: 6px 12px;
    margin: 2px;
    
    /* Appearance */
    background-color: #4a89dc;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    
    /* Transitions */
    transition: background-color 0.2s;
    
    /* Text */
    text-align: center;
    text-decoration: none;
    text-transform: none;
    
    /* Prevent text selection */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

button:hover, 
input[type="button"]:hover, 
input[type="submit"]:hover {
    background-color: #3a79cc;
}

button:active, 
input[type="button"]:active, 
input[type="submit"]:active {
    background-color: #2a69bc;
}

button:disabled, 
input[type="button"]:disabled, 
input[type="submit"]:disabled {
    background-color: #a0a0a0;
    cursor: not-allowed;
}

/* Override any button-specific styles that change font, size or padding */
#showRaceClockBtn, #resetBtn, #stopRaceClockBtn, #showClockBtn, #reloadBtn, #finishMessageBtn,
#saveCompBtn, #saveBoardBtn, #messageBtn, #clearMessgeBtn,
#uploadImageBtn, #showVideoBtn, #addBoardBtn,
#setInfiniteMessageDelayBtn, #setInfiniteImageDelayBtn, #setInfiniteVideoDelayBtn, #setInfiniteSingleDelayBtn,
#showTSBtn, #showTRBtn, #showFSBtn, #showFRBtn, #showFIBtn, #showTIBtn,
.ctrl-btn, .remove-board-btn {
    /* Reset to standard values */
    font-family: Arial, sans-serif;
    font-size: 13px;
    padding: 6px 12px;
    font-weight: normal;
}

/* Only keep color differences */
.remove-board-btn, #clearMessgeBtn {
    background-color: #dc4a4a;
}

.remove-board-btn:hover, #clearMessgeBtn:hover {
    background-color: #cc3a3a;
}

/* Comprehensive update for all green buttons */
button, 
input[type="button"], 
input[type="submit"] {
    /* Default blue for most buttons */
    background-color: #4a89dc;
}

/* Target all green buttons by ID and class */
#saveCompBtn, #saveBoardBtn, #messageBtn, #uploadImageBtn, #showVideoBtn, #addBoardBtn,
#setCompBtn, #setBoardBtn, #sendMessageBtn, #sendImageBtn, #sendVideoBtn,
#saveEventBtn, #saveSettingsBtn, #saveConfigBtn, #savePreferencesBtn,
button.green, button.success, button.save, button.add, button.upload, button.send,
input[type="button"].green, input[type="button"].success, input[type="button"].save,
input[type="submit"].green, input[type="submit"].success, input[type="submit"].save {
    background-color: #4a89dc !important; /* Change from orange to blue */
}

/* Hover states for buttons */
#saveCompBtn:hover, #saveBoardBtn:hover, #messageBtn:hover, #uploadImageBtn:hover, #showVideoBtn:hover, #addBoardBtn:hover,
#setCompBtn:hover, #setBoardBtn:hover, #sendMessageBtn:hover, #sendImageBtn:hover, #sendVideoBtn:hover,
#saveEventBtn:hover, #saveSettingsBtn:hover, #saveConfigBtn:hover, #savePreferencesBtn:hover,
button.green:hover, button.success:hover, button.save:hover, button.add:hover, button.upload:hover, button.send:hover,
input[type="button"].green:hover, input[type="button"].success:hover, input[type="button"].save:hover,
input[type="submit"].green:hover, input[type="submit"].success:hover, input[type="submit"].save:hover {
    background-color: #3a79cc !important; /* Darker blue on hover */
}

/* Target any buttons with inline styles using attribute selectors */
button[style*="background-color:#4adc89"], 
button[style*="background-color: #4adc89"],
button[style*="background-color:#3acc79"], 
button[style*="background-color: #3acc79"],
button[style*="background-color:rgb(74, 220, 137)"], 
button[style*="background-color: rgb(74, 220, 137)"],
button[style*="background-color:rgb(58, 204, 121)"], 
button[style*="background-color: rgb(58, 204, 121)"] {
    background-color: #4a89dc !important;
}

/* Fix jQuery UI button conflicts */
.ui-button {
    font-family: Arial, sans-serif !important;
    font-size: 13px !important;
    padding: 6px 12px !important;
}

/* Special button styles */
.remove-board-btn {
    background-color: #dc4a4a;
    padding: 3px 8px;
    font-size: 12px;
    margin-left: 10px;
}

.remove-board-btn:hover {
    background-color: #cc3a3a;
}

/* Action buttons - slightly larger */
#showRaceClockBtn, #resetBtn, #stopRaceClockBtn, #showClockBtn, #reloadBtn, #finishMessageBtn {
    padding: 8px 15px;
    margin: 5px;
}

/* Save buttons - change from green to blue */
#saveCompBtn, #saveBoardBtn {
    background-color: #4a89dc;
    padding: 8px 15px;
    font-weight: bold;
}

#saveCompBtn:hover, #saveBoardBtn:hover {
    background-color: #3a79cc;
}

/* Message buttons */
#messageBtn {
    background-color: #4a89dc; /* Change from green to blue */
    padding: 8px 15px;
    font-weight: bold;
}

#messageBtn:hover {
    background-color: #3a79cc;
}

#clearMessgeBtn {
    background-color: #dc4a4a; /* Red like remove buttons */
    padding: 3px 8px;
    font-size: 12px;
    margin-left: 10px;
}

#clearMessgeBtn:hover {
    background-color: #cc3a3a;
}

/* Image and video buttons */
#uploadImageBtn, #showVideoBtn {
    background-color: #4a89dc; /* Change from green to blue */
    padding: 8px 15px;
}

#uploadImageBtn:hover, #showVideoBtn:hover {
    background-color: #3a79cc;
}

/* Infinite delay buttons - change from purple to blue */
#setInfiniteMessageDelayBtn, #setInfiniteImageDelayBtn, #setInfiniteVideoDelayBtn, #setInfiniteSingleDelayBtn {
    background-color: #4a89dc; /* Change from purple to blue */
    font-size: 12px;
}

#setInfiniteMessageDelayBtn:hover, #setInfiniteImageDelayBtn:hover, #setInfiniteVideoDelayBtn:hover, #setInfiniteSingleDelayBtn:hover {
    background-color: #3a79cc;
}

/* Event selection buttons */
#showTSBtn, #showTRBtn, #showFSBtn, #showFRBtn, #showFIBtn, #showTIBtn {
    background-color: #3498db; /* Blue for selection */
}

#showTSBtn:hover, #showTRBtn:hover, #showFSBtn:hover, #showFRBtn:hover, #showFIBtn:hover, #showTIBtn:hover {
    background-color: #2980b9;
}

/* Add board button */
#addBoardBtn {
    background-color: #4a89dc; /* Change from green to blue */
}

#addBoardBtn:hover {
    background-color: #3a79cc;
}

/* Checkbox container styling */
.checkbox-container {
    margin: 5px 0;
    padding: 5px;
    border-radius: 3px;
}

.checkbox-container:hover {
    background-color: #f5f5f5;
}

/* Tab styling improvements */
#controller-tabs {
    margin-top: 15px;
}

.ui-tabs .ui-tabs-nav {
    background: #4a89dc;
    border-radius: 3px 3px 0 0;
    padding: 5px 5px 0 5px;
}

.ui-tabs .ui-tabs-nav li {
    margin: 0 3px 0 0;
}

.ui-tabs .ui-tabs-nav li a {
    color: white;
    font-weight: bold;
    padding: 8px 12px;
}

.ui-tabs .ui-tabs-nav li.ui-tabs-active {
    margin-bottom: 0;
    padding-bottom: 1px;
}

.ui-tabs .ui-tabs-nav li.ui-tabs-active a {
    color: #4a89dc;
}

.ui-tabs .ui-tabs-panel {
    padding: 15px;
    border: 1px solid #ddd;
    border-top: none;
}

/* Fieldset styling */
fieldset {
    border: 2px solid #3a79cc;
    border-radius: 3px;
    padding: 10px 15px;
    margin: 10px 0;
}

legend {
    font-weight: bold;
    color: #4a89dc;
    padding: 0 10px;
}

/* Reset the Clear Message button to match standard buttons */
#clearMessgeBtn {
    /* Reset all properties to match standard buttons */
    font-family: Arial, sans-serif;
    font-size: 13px;
    font-weight: normal;
    padding: 6px 12px;
    margin: 2px;
    border-radius: 3px;
    
    /* Only keep the red color */
    background-color: #dc4a4a;
}

#clearMessgeBtn:hover {
    background-color: #cc3a3a;
}

/* Remove any special class that might be applied */
.remove-board-btn#clearMessgeBtn {
    /* Override any .remove-board-btn styles */
    font-size: 13px;
    padding: 6px 12px;
    margin: 2px;
}

/* Improve readability in the Competition Information tab */
#tab-competition {
    line-height: 1.6;
    padding: 15px;
}

#tab-competition > div {
    margin-bottom: 15px;
}

#tab-competition label {
    margin-right: 8px;
}

#tab-competition input[type="text"],
#tab-competition input[type="number"] {
    padding: 5px;
    margin: 5px 0;
}

#tab-competition .eventNameHelper {
    margin-top: 5px;
    color: #666;
    font-style: italic;
    font-size: 0.9em;
}

/* Improve radio button spacing */
#compType {
    display: inline-flex;
    align-items: center;
    margin-left: 5px;
}

#compType input[type="radio"] {
    margin-right: 3px;
    margin-left: 10px;
}

#compType input[type="radio"]:first-child {
    margin-left: 0;
}

/* Improve spacing for the competition ID and name */
#tab-competition > div:nth-child(2) {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

#tab-competition > div:nth-child(2) > div {
    display: flex;
    align-items: center;
}

#tab-competition #compId {
    margin-left: 5px;
    font-weight: bold;
}

/* Add some visual separation between sections */
#tab-competition > div:not(:last-child) {
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

/* Styles for the new competition information structure */
.comp-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
}

.comp-section:not(:last-child) {
    border-bottom: 1px solid #eee;
}

.comp-field {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.comp-field label {
    min-width: 150px;
    font-weight: bold;
}

.comp-field input[type="text"],
.comp-field input[type="number"] {
    flex: 1;
    max-width: 400px;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 3px;
}

.button-section {
    margin-top: 25px;
    border-bottom: none !important;
    padding-bottom: 0 !important;
}

/* Improve the helper text */
.eventNameHelper {
    margin-top: 8px;
    margin-left: 150px;
    color: #666;
    font-style: italic;
    font-size: 0.9em;
}


/* Standardized button sizing for ALL buttons */
button, 
input[type="button"], 
input[type="submit"],
.ui-button,
.ctrl-btn,
.remove-board-btn {
    /* Consistent size */
    padding: 8px 15px;
    margin: 4px;
    min-height: 36px;
    min-width: 80px;
    
    /* Consistent font */
    font-family: Arial, sans-serif;
    font-size: 1em !important; /* Force 1em font size */
    font-weight: normal;
    
    /* Consistent appearance */
    background-color: #4a89dc;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    
    /* Transitions */
    transition: background-color 0.2s;
    
    /* Text */
    text-align: center;
    text-decoration: none;
    text-transform: none;
    
    /* Prevent text selection */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Override jQuery UI button styles to match our standard */
.ui-button {
    font-family: Arial, sans-serif !important;
    font-size: 1em !important;
    padding: 8px 15px !important;
    min-height: 36px !important;
    min-width: 80px !important;
}

/* Small buttons exception - for buttons that need to be smaller */
.btn-small,
.remove-board-btn,
#clearMessgeBtn {
    min-width: 60px;
    min-height: 28px;
    padding: 4px 10px;
    font-size: 1em !important; /* Keep font size consistent */
}

/* Fix any specific button overrides that might be causing inconsistency */
#showRaceClockBtn, #resetBtn, #stopRaceClockBtn, #showClockBtn, #reloadBtn, #finishMessageBtn,
#saveCompBtn, #saveBoardBtn, #messageBtn, #clearMessgeBtn,
#uploadImageBtn, #showVideoBtn, #addBoardBtn,
#setInfiniteMessageDelayBtn, #setInfiniteImageDelayBtn, #setInfiniteVideoDelayBtn, #setInfiniteSingleDelayBtn,
#showTSBtn, #showTRBtn, #showFSBtn, #showFRBtn, #showFIBtn, #showTIBtn,
#addCustomTimeBtn {
    /* Reset to standard values */
    padding: 8px 15px;
    min-height: 36px;
    min-width: 80px;
    font-size: 1em !important;
    font-weight: normal;
}

/* Message Styles tab styling */
.message-style-row {
    margin: 15px 0;
    display: flex;
    align-items: center;
}

.message-style-row label {
    width: 120px;
    display: inline-block;
    font-weight: bold;
}

.message-style-row .style-preview {
    min-width: 200px;
    padding: 5px 10px;
    margin-left: 10px;
    border: 1px dashed #aaa;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
}

#saveMessageStylesBtn {
    margin-right: 15px;
    background-color: #4a89dc;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 3px;
    cursor: pointer;
}

#saveMessageStylesBtn:hover {
    background-color: #3a79cc;
}

#tab-message-styles fieldset {
    margin-bottom: 20px;
}
